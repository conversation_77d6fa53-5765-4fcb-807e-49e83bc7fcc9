export const TR_EN = {
  More_information: 'Need more assistance ?',
  SearchBar: 'Looking for a piece of tool or equipment ?',
  SearchBar2: 'Use our advanced search tool to get your exact need',
  Browse_popular_categories: 'Browse popular categories',
  Elevation_and_scaffolding: 'Elevation and scaffolding',
  Earthmoving: 'Earthmoving',
  Energy_and_air: 'Energy and air',
  Handling: 'Handling',
  Landscaping: 'Landscaping',
  Specialized_tooling: 'Specialized tooling',
  Fluid_solutions: 'Fluid solutions',
  Event_reception: 'Event and reception',
  Site_solution: 'Site solution',
  Notifications: 'Notifications',
  Settings: 'Settings',
  Help: 'Help',
  English: 'English',
  French: 'French',
  Welcome_to_tooler: 'Welcome to Derental',
  Welcome_to_tooler_renter: 'Welcome to Derental as a renter',
  Welcome_paragraph1:
    'Our mission is to get you the freedom to book your equipment faster & easier anytime and anywhere.',
  Equipment: 'Equipment',
  Where: 'City',
  Rent_on: 'From',
  Return_on: 'To',
  Select_equipment: 'Select Equipment',
  Set_your_location: 'Select your location',
  Text_location: 'Please enter your location and rental dates',
  Add_date: 'Add date',
  Help_you: 'Let one of our agents help you',
  Name: 'Name',
  Email: 'Email',
  Phone_number: 'Phone number',
  Company: 'Company',
  Job: 'Jobs',
  Press: 'Press',
  Blog: 'Blog',
  Our_story: 'Our story',
  Use_cases: 'Use-cases',
  Legal: 'Legal',
  Terms_condition: 'Terms & Conditions',
  Legal_info: 'Legal information',
  Privacy_notice: 'Privacy notice',
  Address: 'Address',
  Are_you_a: 'Are you a',
  Person_name: 'Full name',
  Personal_address: 'Personal address',
  Country: 'Country',
  Zip_code: 'Zip code',
  State: 'State',
  City: 'City',
  Email_address: 'Email address',
  Username: 'Username',
  Password: 'Password',
  Confirm_your_password: 'Confirm your password',
  Start_your_renting_journey: 'Start your renting journey',
  Credit_para1:
    'In order to finish your sign-up and start renting equipment everywhere and anytime',
  Credit_para2: "YOU'll NEED TO",
  Credit_para3: 'Fill a Credit Check Application',
  Credit_check_form: 'Credit Check Application',
  OR: 'OR',
  Credit_para4: 'Add a credit card to your profile',
  Credit_card: 'Credit card',
  Welcome_back: 'Welcome back',
  Forget_your_password: 'Forget your password ?',
  Login: 'Sign in',
  Register: 'Sign up',
  Logout: 'Logout',
  You_dont_have_an_account: "You don't have an account ?",
  Create_an_account: 'Create one',
  As_account: 'as a',
  Renter_account: 'Renter',
  Failed_to_login: 'Invalid credentials',
  Failed_to_register: 'Failed to create account !',
  Please_enter_your_password: 'Please enter your password',
  Please_enter_a_valid_email: 'Please enter a valid email',
  Please_enter_your_email: 'Please enter your email',
  Please_enter_your_user_name: 'Please enter your user name',
  Please_enter_your_phone_number: 'Please enter your phone number',
  Please_enter_your_city: 'Please enter your city',
  Please_enter_your_state: 'Please enter your state',
  Please_enter_your_zip_code: 'Please enter your zip code',
  Please_enter_your_address: 'Please enter your address',
  Please_enter_your_name: 'Please enter your name',
  Passwords_do_not_match: 'Passwords do not match',
  Company_name: 'Company name',
  Company_address: 'Company address',
  Full_address: 'Full address',
  Email_address_company: 'Email address',
  Phone_number_company: 'Phone number',
  Username_company: 'Username',
  Password_company: 'Password',
  Confirm_your_password_company: 'Confirm your password',
  Become_equipper: 'Become an Equipper',
  Forget_Password: 'Forget Password',
  Forget_password_text:
    'Please enter your email address to receive a verification code',
  Send: 'Send',
  Check_your_inbox: 'Check your inbox',
  Description_code_verification: 'A verification code has been sent to',
  Resend_code: 'Resend code',
  Change_password: 'Change password',
  Verification_code: 'Verification code',
  Enter_code: 'Please enter the 6 digit code sent to your email',
  Verify: 'Verify',
  Create_new_password: 'Create new password',
  Save: 'Save',
  Equipment_details: 'Equipment details',
  Choose_filter: 'Choose filter',
  Not_available: 'Not available',
  Long_lorem:
    "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
  Short_lorem:
    "Lorem Ipsum has been the industry's standard dummy text ever since the 1500s",
  Usages_of_this_equipment: 'Usages of this equipment',
  Additional_important_information: 'Additional Important Information',
  Comments: 'Comments',
  No_comments: 'No comments',
  Book_now: 'Book Now',
  Book_now_company: 'Add to cart',
  Operating_weight: 'Operating weight',
  Engine_output: 'Engine output',
  Emission_stage: 'Emission stage',
  Backhoe_bucket_capacity: 'Backhoe bucket capacity',
  Be_careful_this_product_required_competence_card_to_use_it:
    'Be careful this product required competence card to use it',
  Take_time: 'Please take the time to read the ',
  Equipment_usage_below: 'Equipment usage below ',
  carefully: 'Carefully',
  Get_in_touch: 'Get in touch with ',
  At: 'at ',
  Card_holer_name: 'Card holder name',
  Card_number: 'Card number',
  Expiration_date: 'Expiration Date',
  Cvv: 'CVV',
  Sixteen_digit_number: '16-digit number',
  Credit_card_type: 'Credit Card Type',
  DD_MM_YYYY: 'DD/MM/YYYY',
  Three_digit_number: '3-digit number',
  Page_help_title1: 'How can we help you ?',
  Page_help_search_placeholder: 'Write a question or a problem',
  Page_help_toolo_bull1: 'Stuck somewhere ?',
  Page_help_toolo_bull2: 'Don’t worry ! we are here to help',
  Page_help_title2: 'Popular frequently questions',
  Get_Answers: 'GET ANSWERS',
  Page_help_question1: 'How can i filter on the map ?',
  Page_help_question2: 'How can i become an equipper ?',
  Page_help_question3: 'How can i add an equipment ?',
  Page_help_question4: 'How can i invite a member ?',
  Start_your_equipper_journey:
    'Start your equipper journey by joining the Derental family today',
  Become_an_equipper: 'Become an equipper',
  Week: 'Week',
  Filters: 'Filters',
  Maps: 'Maps',
  We_found: 'We found ',
  For_your_need: 'for your need',
  Start_your_journey_button:
    'Start your equipper journey by joining the Derental family today',
  Add_equipment_button: 'Add equipment',
  Upload_Excel_file_text_1: 'Upload Excel file',
  Upload_Excel_file_text_2: 'Click here to upload Excel file',
  Upload_airtable: 'Upload through Airtable',
  Upload_excel_file_button: 'Start uploading',
  Submit_button: 'Submit',
  View_more_button: 'Show more',
  Search_button: 'Search',
  Search_placeholder: 'Search here ...',
  Edit_my_profile_text: 'Edit my profile',
  Change_logo_text: 'Change your logo ? ',
  Upload_new_logo_text: 'Upload a new one here',
  Recommended_size_text: '( Recommended size 800/800px )',
  Upload_button: 'Upload',
  Update_button: 'Update',
  Requests_management_title: 'Reservations',
  Bidz_management_title: 'Bidz management',
  Price_offer_per_week: 'Price offer per week',
  Equipments_management_title: 'Equipment management',
  Team_management_details_title: 'Team Management & Details',
  In_progress_rentals_title: 'In progress rentals',
  Pricing_marketing_title: 'Exclusive Offers',
  Comments_rating_title: 'Comments & Rating',
  Pop_up_success_text:
    'Your reclamation has been sent an agent will contact you soon',
  Close_button: 'Close',
  Manage_members_title: 'Manage members',
  Manage_projects_title: 'Manage projects',
  Rentals_summary_title: 'Rentals history',
  EMP_welcome_text: 'Welcome to Derental',
  EMP_welcome_text_equipper: 'Welcome to Derental, as an Equipper',
  EMP_signup_text:
    'Our mission is to get you the digital notoriety and real-time inventory access to be one step ahead of your competition',
  EMP_signup_text2: 'Build your business with Derental',
  EMP_signup_text3:
    'Please sign up to start manage your equipment as a location center',
  Success_text: 'Success',
  Invite_text: 'Invite',
  Book_button: 'Book Now',
  Week_text: 'WEEK',
  Comments_text: 'comments)',
  Day_text: 'DAY',
  Day: 'Day',
  Days_with_space: ' Days',
  Four_Week_text: '4 WEEKS',
  Four_week: '4 weeks',
  Password_restriction_text1: 'Lowercase letter',
  Password_restriction_text2: 'Capital letter',
  Password_restriction_text3: 'Number',
  Password_restriction_text4: 'Special character',
  Accepted_text: 'Accepted',
  Declined_text: 'Declined',
  Pending_text: 'Pending',
  Sent_text: 'Sent',
  All_requests_text: 'All requests',
  Equipment_name: 'Equipment Name',
  Derental_equipment_name: 'Equipment Name (Derental convention) ',
  Renter_name: 'Renter name',
  Equipper_name: 'Equipper name',
  From_to: 'From - to',
  Total_amount: 'Total amount',
  Actions: 'Actions',
  Password_tooltip:
    'Password must be at least 8 characters long and contain at least one number and one uppercase, lowercase letter and special character',
  Required: 'Required',
  SignUp_name_max_length: 'Name must be at under 50 characters',
  SignUp_address_max_length: 'Address must be at under 50 characters',
  SignUp_phone_number_max_length: 'Phone number must be at under 15 characters',
  SignUp_user_name_max_length: 'User name must be at under 15 characters',
  Invalid_phone_number: 'Invalid phone number, exp: +33 6 12 34 56 78',
  Invalid_email: 'Invalid email',
  signUp_password_match: 'password must match',
  Under_construction: 'This section is under construction',
  Rented_on: 'Rented on',
  Return_date: 'Return date',
  Location: 'Location',
  Request_management_no_request:
    'There are no equipment here, start adding equipment',
  History_of_rented_equipments: 'History of rented equipment',
  Your_history_is_empty: 'Your history is empty',
  Rent_date: 'Rent date',
  Start_date: 'Start date',
  End_date: 'End date',
  Start_End_date: 'Start & End date',
  Member: 'Member',
  Total_rental_income: 'Total rental income',
  Total_rented_equipments: 'Total rented equipment',
  Number_of_rented_equipments: 'Number of rented equipment',
  Rating: 'Rating',
  Lodger_name: 'Renter name',
  Top_rented_equipments: 'Top rented equipment',
  Total_rented_equipments_per_locations: 'Total rented equipment per locations',
  Edit_my_profile: 'Edit my profile',
  To: 'to ',
  From: 'from',
  Decline: 'Decline',
  Accept: 'I approve Derental’s ',
  Accept_booking: 'Accept',
  Cancel: 'Cancel',
  Are_you_sure_you_want_to_delete: 'Are you sure you want to delete',
  Are_you_sure_you_want_to_do_this: 'Are you sure you want to do this',
  Delete: 'Delete',
  I_m_not_sure: "I'm not sure",
  SignUp: 'Sign Up',
  RateUs: 'Rate Us',
  Home: 'Home',
  Admin_tab_empty_message: 'You have no admin for the moment',
  Affect_to: 'Affect to',
  Advanced_filters: 'Rental details',
  Clear_all: 'Clear all',
  Date: 'Date',
  Weight: 'Weight : ',
  Height: 'Height : ',
  Width: 'Width : ',
  Length: 'Length : ',
  Force: 'Force : ',
  Volt: 'Volt : ',
  Kw: 'KW : ',
  Btu: 'BTU : ',
  Price_per: 'Price per',
  Price_per_day: 'Price per day',
  Price: 'Price',
  Month: 'Month',
  Price_range: 'Price range : ',
  More_filters: 'More filters',
  Rental_details: 'Rental details',
  Sub_category: 'Sub categories',
  More_filters_are_under_construction: 'More filters are under construction',
  Old_password_text: 'Old password',
  Close_account_text: 'Close account',
  Close_account_text2:
    ' Please make sure before proceeding as your account will be permanently deleted',
  Save_changes_text: 'Save changes',
  Saved_cards_text: 'Saved cards',
  Card_ending_text: 'Card ending in ',
  Add_Card_text: 'Add new payment method',
  Card_credentials_text: 'Card holder name',
  Card_credentials_text2: 'Card number',
  Card_credentials_text3: 'Expiration date',
  Card_credentials_text4: 'CVV',
  Card_credentials_tooltip1: '16 - Digit number',
  Card_credentials_tooltip2: 'MM/DD/YY',
  Card_credentials_tooltip3: '3 - Digit number',
  Add_card_button: 'Add card',
  Credit_check_form_text1: 'Do you have property insurance : ',
  Credit_check_form_text2: 'Order number required for renting',
  Credit_check_form_text3: 'Project number required for renting : ',
  Credit_check_form_text4: ' Tax-Free entity :',
  Credit_check_form_text5: 'Do you have other accounts',
  Credit_check_form_title: 'Credit references ',
  Credit_check_form_title2: 'Contacts',
  Credit_check_form_title3: 'Accounts payable ',
  Credit_check_form_title4: 'Person in charge of rentals',
  Credit_check_form_title5: 'Bond, authorized person / authorized signature',
  Yes_button: 'Yes',
  No_button: 'No',
  Email_not_found: 'Email not found',
  Edit: 'Edit',
  Invite_new_member: 'Invite new member',
  Add_many_members_at_once: 'Add many members at once',
  Send_invitation: 'Send invitation',
  Copy: 'Copy',
  Invite_link_description:
    'Guests will be notified per email. This link will be shared to invite new members to projects',
  Edit_admin_member: 'Edit admin member',
  Validate: 'Validate',
  Power_collaborator: 'Power Collaborators',
  Collaborator: 'Collaborators',
  Account_manager: 'Account Managers',
  Administrator: 'Administrators',
  You_currently_have_no_projects_LMP: 'You currently have no projects',
  Project_name_LMP: 'Project Name',
  Project_details_LMP: 'Project Details',
  Members_LMP: 'Members Name',
  Equipments_LMP: 'Conventional Equipment Name',
  Delivery_address_LMP: 'Delivery Address',
  Billing_address_LMP: 'Billing Address',
  Street: 'Street',
  Create_project_LMP: 'Create project',
  Projects_LMP: 'Projects',
  Credit_check_form_name_LMP: 'Credit Check Application name',
  Edit_project_LMP: 'Edit project',
  Project_name_required_LMP: 'Project name is required',
  State_required_LMP: 'State is required',
  City_required_LMP: 'City is required',
  Address_required_LMP: 'Address is required',
  Zip_code_required_LMP: 'Zip code is required',
  Street_required_LMP: 'Street is required',
  Update_project_LMP: 'Update project',
  View_form_details: 'View form details',
  Saved_forms: 'Saved forms',
  Saved_on: 'Saved on',
  Credit_check_form_title0: 'Credit Check Application title',
  Year_of_creation: 'Year of creation',
  Number_of_employees: 'Number of employees',
  Select_type: 'Select type',
  Delete_form: 'Delete form',
  Rename_form: 'Rename form',
  Set_as_default: 'Set as default',
  Login_button: 'Login',
  Signup_success: 'Account created successfully',
  Reclamation_success:
    'Thank you for reaching out. One of our team members will be in touch with you very soon !',
  Rental_date: 'Rental date',
  Rental_amount: 'Rental amount',
  View_less: 'View less',
  View_more: 'View more',
  Booking_popup_description:
    'Are you sure you would like to proceed with the following booking ?',
  Bidz_Booking_popup_description:
    'Are you sure you want to ask for a quotation for these equipment details?',
  Booking_success_message:
    'The booking request has been sent successfully. We will notify you when getting the response from the owner',
  Category_word: 'category',
  In_word: 'in',
  Failed_Password: 'Wrong password',
  Failed_email: 'Wrong email',
  Code_verification_sent: 'Verification code has been sent !',
  Excel_csv_files: 'Excel/CSV files',
  Equipment_added_successfully: 'File uploaded successfully',
  No_more_equipments: 'No more equipment',
  Renter_management_portal: 'Renter management portal',
  Equipper_management_portal: 'Equipper management portal',
  Confirmation_booking_label: 'Confirmation under : ',
  Type_of_propulsion: 'Type of propulsion : ',
  Add_admin_modal_main_message: "We've been able to add",
  Add_admin_modal_secondary_message:
    'We encountered some difficulties processing the others',
  Please_try_again_later: 'please try again later',
  Delete_member_main_message: 'Something went wrong with the delete action',
  Update_member_main_message: 'Something went wrong with the Update action',
  Matches_found: 'We’ve been able to find what you are looking for : ',
  No_matches_found:
    'Let us help you determine the right equipment for your needs.',
  Tooler_found:
    'There is no available equipment through our affiliated rental centers. No problem, Derental.bidz will get you your needed equipment.',
  Matches_equippers_found: 'We found your equipment here',
  Matches_equippers_found_text:
    'Equippers we trust, that have what you’re looking for',
  Equipment_specifications: 'Equipment specifications',
  Equipments: 'Equipment(s)',
  Explore_all: 'Explore all',
  Ask_for_quota: 'Ask for quota',
  Bidz_request_success: 'Your bidz request has been sent successfully',
  Year_of_location_required: 'Year of location is required',
  Year_of_location_length: 'Year of location must be 4 digits',
  Address_min_length: 'Address must be at least 5 characters',
  Address_required: 'Address is required',
  City_min_length: 'City must be at least 3 characters',
  City_required: 'City is required',
  Prov_min_length: 'Province must be at least 3 characters',
  Country_min_length: 'Country must be at least 2 characters',
  Prov_required: 'Province is required',
  Country_required: 'Country is required',
  Postal_code_length: 'Postal code must be exactly 6 characters',
  Postal_code_required: 'Postal code is required',
  Number_of_tax_free_years: 'Number of tax-free years',
  Number_of_tax_free_years_required: 'Number of tax-free years is required',
  Number_of_tax_free_years_length: 'Number of tax-free years must be 1 digit',
  Ext_length: 'Ext must be at least 1 digit',
  Invalid_number_of_employees: 'Invalid number',
  Invalid_year_creation: 'Invalid year of creation',
  Number_of_employees_required: 'Number of employees is required',
  President_name_required: 'President name is required',
  President_name_length: 'President name must be at least 3 characters',
  President_email_required: 'President email is required',
  President_email_invalid: 'President email is invalid',
  President_name: 'President name',
  Supplier: 'Supplier',
  Postal_code: 'Postal code',
  Supplier_min_length: 'Supplier must be at least 3 characters',
  Name_min_length: 'Name must be at least 3 characters',
  Name_required: 'Name is required',
  Ext_required: 'Ext is required',
  Title_required: 'Title is required',
  Phone_number_required: 'Phone number is required',
  Email_required: 'Email is required',
  Date_required: 'Date is required',
  Signature_required: 'Signature is required',
  Invalid_date: 'date format must be DD/MM/YYYY',
  Title: 'Title',
  Telephone: 'Telephone',
  Ext: 'Ext.',
  Cell: 'Cell',
  Full_name: 'Full name',
  Function: 'Function',
  Customer_code: 'Customer code',
  Customer_code_required: 'Customer code is required',
  Name_your_credit_check_form_to_recognized_later:
    'Name your Credit Check Application to recognized later',
  Credit_check_form_title_required:
    'Credit Check Application title is required',
  Status: 'Status',
  Price_offer: 'Price offer',
  Please_wait: 'Please wait',
  Download: 'Download',
  Update: 'Update',
  You_have_no_credit_check_forms: 'You have no Credit Check Applications',
  Credit_check_form_details: 'Credit Check Application details',
  Download_credit_check_form: 'Download Credit Check Application',
  Options: 'More options',
  Create_new_credit_check_form: 'Create a new Credit Check Application',
  Bidz_requests: 'BIDZ requests',
  Bidz_offer: 'BIDZ offers',
  Not_specified: 'N/A',
  PO_number: 'Purchase order number',
  Credit_account: 'Credit account',
  Pick_up_name: 'Pick-up',
  Delivery_name: 'Delivery',
  Payment_method: 'Payment method',
  Delivery_preference: 'Delivery preference',
  Request_a_quote: 'Request a quote',
  Credit_check_form_is_required_if_you_want_to_pay_with_a_credit_account:
    'Credit Check Application is required if you want to pay with a credit account',
  Explore_tooler_bidz: 'Explore Derental bidz',
  View_details: 'View details',
  Make_bid: 'Make a bid',
  Brand: 'Brand : ',
  Equipment_prupose_use: 'Equipment purpose use ',
  Contact: 'Contact',
  Send_bid: 'Send a bid',
  Cfm: 'CFM : ',
  Affected_equipments: 'Affected equipment',
  Affected_members_emails: 'Affected members emails',
  Delivery: 'Delivery',
  Customer_service: 'Customer service',
  View_all: 'View all',
  Customer_feedback: 'Customer feedback',
  What_our_clients_said_about_us: 'What our clients said about us',
  Are_you_sure_you_want_to_accept_this_offer:
    'Are you sure you want to accept this offer ?',
  Are_you_sure_you_want_to_decline_this_offer:
    'Are you sure you want to decline this offer ?',
  Equipment_card_standard_help_message:
    'Unlock the full potential of your equipment search by using advanced filters',
  Explore: 'Explore other products',
  Are_you_sure_you_want_to_delete_project:
    'Are you sure you want to delete this project',
  Are_you_sure_you_want_to_do_detete_equipment:
    'Are you sure you want to delete this equipment',
  Opening_hours_text: 'Opening hours',
  Quotes_confirmation_under: 'Quotes confirmation under',
  Company_highlights: 'Company highlights',
  Client_service: 'Client service',
  Fast_delivery: 'Fast delivery',
  Competitive_prices: 'Competitive prices',
  Are_you_sure_you_want_to_create_this_credit_check_form:
    'Are you sure you want to create this Credit Check Application?',
  Are_you_sure_you_want_to_update_this_credit_check_form:
    'Are you sure you want to update this Credit Check Application ?',
  Equipper_email: 'Equipper email',
  Equipper_phone: 'Equipper phone',
  Renter_email: 'Renter email',
  Are_you_sure_you_want_to_declined_this_request:
    'Are you sure you want to decline the equipment request',
  Bidz_management_no_request:
    'There are no BIDZ request here, start adding BIDZ',

  Are_you_sure_you_want_to_declined_this_offer:
    'Are you sure you want to decline this offer ?',
  Bidz_management_no_offer: 'There are no BIDZ offer here, start adding BIDZ',
  Cancled_text: 'Canceled',
  No_more_pending_request: 'No more pending request',
  No_more_accepted_offers: 'No more accepted offers',
  No_more_declined_offers: 'No more declined offers',
  No_more_accepted_request: 'No more accepted request',
  Accept_bidz_offer_unable_to_send_Bids_confirmation_offer_to_equipper_error:
    'We are unable to send email to equipper due to error',
  Accept_bidz_offer_unable_to_send_Bidz_canceled_offer_to_equipper_error:
    'We are unable to send email to equipper due to error',
  No_more_declined_request: 'No more declined requests',
  Cancelation_reason: 'You have to add your cancelation reason(s) here',
  Rejection_reason: 'You have to add your rejection reason(s) here',
  Are_you_sure_you_want_to_cancel_this_offer:
    'Are you sure you want to cancel this offer ?',
  No_more_canceled_offers: 'No more canceled offers',
  Comment_is_required: 'Your reason for cancelation is required',
  Your_reason_for_cancelation_must_be_at_least_10_characters:
    'Your reason for cancelation must be at least 10 characters',
  Your_reason_for_rejection_must_be_at_least_10_characters:
    'Your reason for rejection must be at least 10 characters',
  Upload_equipments_error_http: 'No such file',
  You_have_no_power_collaborator_for_the_moment:
    'You have no power collaborator for the moment',
  You_have_no_account_manager_for_the_moment:
    'You have no account manager for the moment',
  You_have_no_collaborator_for_the_moment:
    'You have no collaborator for the moment',
  You_will_find_your_in_progress_rentals_here:
    'You will find your in progress rentals here',
  Promotions_on_equipments: 'Promotions on equipment',
  This_section_is_under_construction: 'This section is under construction',
  Promotions_for_lodgers: 'Promotions for renters',
  Apply_promotion: 'Apply promotion',
  This_field_is_required: 'This field is required',
  Categories: 'Categories',
  Create_project_error_lodger_id_is_required: 'Renter id is required',
  Create_project_error_unable_to_set_project_with_id:
    'Unable to set project with id',
  Create_project_error_unable_to_add_project_error: 'Unable to add project',
  Create_project_error_unable_to_update_lodger_error: 'Unable to update renter',
  Create_project_error_unable_get_lodger_error: 'Unable to get renter',
  Create_project_error_unable_to_get_lodger_error: 'Unable to get lodger',
  Update_project_error_lodger_id_is_required: 'Renter id is required',
  Update_project_error_unable_to_set_project_with_id: ' Unable to set project',
  Update_project_error_unable_to_get_project_with_project_id:
    'Unable to get project',
  Update_project_error_unable_to_get_lodger_error: 'Unable to get renter',
  Update_project_error_unable_to_update_lodger_error: 'Unable to update lodger',
  Delete_project_error_unable_to_delete_project_with_id:
    'Unable to delete project',
  Delete_project_error_unable_to_get_project_with_project_id:
    'Unable to get project',
  Delete_project_error_unable_to_get_lodger_error: 'Unable to get renter',
  Delete_project_error_unable_to_update_lodger_error: 'Unable to update lodger',
  Create_member_error_failed_to_get_lodger_by_id: 'Failed to get renter',
  Create_member_error_failed_to_send_invitation_error:
    'Failed to send invitation error',
  Create_member_error_failed_to_get_project_by_id: 'Failed to get project',
  Create_member_error_unable_to_send_invite_email_to_lodger:
    'Unable to send invite email to renter',
  Create_member_error_failed_to_update_lodger: 'Failed to update renter',
  Create_member_error_failed_to_get_member_by_id: 'Failed to get member by id',
  Create_member_error_member_can_not_invite_a_member_with_higher_privilege_level_than_his_own:
    "Member can't invite a member with higher privilege level than his own",
  Create_member_error_unable_to_send_invite_email_to_renter:
    'Unable to send invite email to renter',
  Create_member_error_failed_to_update_equipper: 'Failed to update equipper',
  Create_member_error_unable_to_send_invite_email_to_equipper:
    'Unable to send invite email to equipper',
  Create_member_error_failed_to_get_equipper_by_id: 'Failed to get equipper',
  Update_member_error_failed_to_get_member_by_id: 'Failed to get member by id',
  Update_member_error_failed_to_get_lodger_by_id: 'Failed to get renter',
  Update_member_error_cannot_update_member_with_higher_privilege_level_than_the_member:
    'Cannot update member with higher privilege level than the member',
  Update_member_error_failed_to_get_equipper_by_id: 'Failed to update equipper',
  Delete_member_error_failed_to_get_lodger_by_id: 'Failed to get renter by id',
  Delete_member_error_failed_to_get_member_by_id: 'Failed to get member by id',
  Delete_member_error_cannot_delete_member_with_higher_privilege_level_than_the_member:
    'Cannot delete member with higher privilege level than the member',
  Delete_member_error_failed_to_get_lodger_by_member_id:
    'Failed to get renter by member id',
  Delete_member_error_failed_to_update_lodger: 'Failed to update renter',
  Delete_member_error_failed_to_delete_member: 'Failed to delete member',
  Affected_bidz_equipments: 'Affected bidz equipment',
  Equipment_prupose_placeholder:
    'Please let us know for each purpose you need the equipment for',
  No_more_pending_offers: 'No more pending offers',
  Bidz_has_equipment: 'has the equipment you are looking for : ',
  Cancelation_reason_text: 'Cancelation reason',
  Rejection_reason_text: 'Rejection reason',
  Email_already_exists: 'Email already exists',
  Communication_preferences: 'Communication preferences',
  SignUp_password_match: 'Confirmation mismatch',
  Rate_us: 'Rate us',
  Start_searching: 'Start searching',
  Explore_searching_results: 'Explore searching results',
  Sort_by: 'Sort by',
  Too_short: 'Too short',
  Too_long: 'Too long',
  Search_result: 'Search results',
  Company_spotlight: 'Company spotlight',
  Products_may_interset: 'products that may interest you',
  Inventory: 'inventory',
  Available: 'Available',
  Sub_categories: 'Sub categories',
  Search_by_category: 'Search by category',
  Equipper_spotlight: 'Equipper spotlight',
  Account_settings: 'Account settings',
  Payment_settings: 'Payment settings',
  Failed_to_change_password: 'Failed to change password',
  Failed_to_verify_code: 'Failed to verify code',
  Password_must_contain:
    'Your password must contain 8 characters, one uppercase, one lowercase, one number and one special case character',
  Upload_image: 'Upload image',
  Copy_image_link: 'Copy image link',
  Credit_check_form_from_name: 'Credit Check Application from name',
  Most_rented: 'Most rented',
  Another_action: 'Another action',
  Something_else: 'Something else',
  All_invitations_sent_successfully: 'All invitations sent successfully',
  Company_spotlight_description: 'Company spotlight description',
  Assign_to_project: 'Assign to project ',
  Company_Name_can_not_be_less_than_3_letters:
    "Company name can't be less than 3 letters",
  Not_found_page_text: 'Sorry, this page isn’t available',
  Not_found_page_description:
    'The link you followed may be broken, or the page may have been removed',
  Your_equipment_exists_please_select_it_from_the_dropdown_menu:
    'Your needed equipment exists in our inventory, but please make a selection from the suggested dropdown list in the search bar.',
  ACCEPT_BIDZ_OFFER: 'Offer accepted but sending email failed',
  Popular_categories_under_construction:
    'Browse by category is under construction. It will be available soon',
  Upload_equipments_error_unable_to_set_equipment_with_id:
    'Unable to set equipment',
  Send_bidz_request_unable_to_get_lodger_with_lodger_id: 'Unable to get lodger',
  Send_bidz_request_can_not_get_all_equippers: 'Can not get all equippers',
  Send_bidz_request_can_not_create_Bids_request: 'Can not create Bids request',
  Send_bidz_request_unable_to_send_Bids_request_to_equipper_error:
    'Unable to send Bids request to equipper',
  Send_bidz_request_unable_to_send_Bids_request_to_renter_error:
    'Unable to send Bids request to renter',
  Accept_bidz_offer_unable_to_get_offer: 'Unable to get offer',
  Accept_bidz_offer_unable_to_accept_offer_with_offer_id_:
    'Unable to accept offer',
  Accept_bidz_offer_unable_to_get_equipper: 'Unable to get equipper',
  Accept_bidz_offer_unable_to_get_lodger: 'Unable to get lodger',
  Accept_bidz_offer_unable_to_send_Bidz_confirmation_offer_to_equipper_error:
    'Unable to send Bidz confirmation offer to equipper',
  Accept_bidz_offer_unable_to_send_Bidz_confirmation_offer_to_renter_error:
    'Unable to send Bidz confirmation offer to renter',
  Accept_bidz_offer_unable_to_get_project_by_id_: 'Unable to get project',
  Accept_bidz_offer_can_not_update_project_with_id_: 'Can not update project',
  Tooler_bidz_standard_help_message:
    'Our affiliated location centers does not have what you are looking for !',
  Tooler_bidz_standard_help_message_2:
    ' Derental.bidz is there to help you get your needed equipment.',
  Cant_load_image: 'Can not load image at the moment',
  Last_name: 'Last name',
  First_name: 'First name',
  Edit_power_collaborator_member: 'Edit power collaborator member',
  Edit_collaborator_member: 'Edit collaborator member',
  Edit_account_manager_member: 'Edit account manager member',
  Requested_name: 'Requested name',
  Coverage_area: 'Service area',
  Select_all: 'Select all',
  No_options: 'No options',
  Waste_solution: 'Waste solution',
  Available_inventory: 'Available inventory',
  Request_free_quote: 'Request a free quote',
  Please_provide_your_location_rental_date:
    'Please provide your location & rental date',
  As: 'As',
  No_priveleges_to_edit:
    "Since you are not the project's owner, you don’t have privileges to edit disabled fields",
  Project_is_required: 'Project is required',
  No_priveleges_to_delete:
    "You don't have the privileges to delete this project(s)",
  Category: 'Category',
  Ladder_and_stepladder: 'Ladder and Stepladder',
  Load_lifting: 'Load lifting',
  Manual_scaffold: 'Manual scaffold',
  Scissor_lift: 'Scissor lift',
  Telehandler: 'Telehandler',
  Work_platform: 'Work platform',
  Forklift: 'Forklift',
  Compaction: 'Compaction',
  Conveyor: 'Conveyor',
  Excavation: 'Excavation',
  Levels_and_measures: 'Levels & measures',
  Loader: 'Loader',
  Moving: 'Moving',
  Pallet_truck: 'Pallet truck',
  Protection: 'Protection',
  Shoring_post: 'Shoring post',
  Trailer: 'Trailer',
  Wheelbarrow: 'Wheelbarrow',
  Compressor: 'Compressor',
  Heating: 'Heating',
  Lighting: 'Lighting',
  Pump: 'Pump',
  Ventilation: 'Ventilation',
  Generator: 'Generator',
  Green_space_maintenance: 'Green space maintenance',
  Trimming_and_pruning: 'Trimming and pruning',
  Cleaning: 'Cleaning',
  Electricity: 'Electricity',
  Painting: 'Painting',
  Plumbing: 'Plumbing',
  Roofing: 'Roofing',
  Welding: 'Welding',
  Concrete_and_masonry: 'Concrete and masonry',
  Ceramic: 'Ceramic',
  Gypsum: 'Gypsum',
  Wood: 'Wood',
  Carpet: 'Carpet',
  Metal_and_steel: 'Metal and steel',
  Boom_lift: 'Boom lift',
  Booked_equipment_alert:
    'You have already sent a booking request for this equipment',
  Book_equipment_error_you_already_have_a_booking_for_this_equipment:
    'You already have a booking for this equipment',
  PO_number_is_required_if_you_want_to_pay_with_a_credit_account:
    'PO number is required if you want to pay with a credit account',
  Rentals_summary_text_1: 'You will find your rentals summary here',
  Empty_rentals_text: 'There are no rented equipment yet',
  No_results_found: 'No results found',
  Select_element_from_dropdown: 'Select element from dropdown',
  You_need_to_be_as_a_renter: 'You need to be logged in as a renter',
  Need_to_login: 'Please login before booking',
  Please_select_a_project:
    'You need to have at least one project before adding new member(s)',
  Event_and_reception: 'Event and reception',
  Site_solutions: 'Site solutions',
  Upload_message:
    'You are able to download your C.C.A. only from the desktop version for the moment',
  Site_solutions_text: 'Site solutions text',
  Pcm: 'PCM : ',
  Description: 'Description',
  Capacity: 'Capacity : ',
  Consumption: 'Consumption : ',
  Credit_check_approval:
    'Credit Check Application approval is subject to minimum 3 credit references',
  Created_at: 'Created at',
  Tooler_commission: 'Derental commission',
  Current_price: 'Current price : ',
  Special_price: 'Special price : ',
  Renter_fees: 'Renter fees',
  Equipper_fees: 'Equipper fees',
  Delivery_cost_text: 'Delivery Cost',
  Delivery_cost_go: 'Delivery drop cost',
  Delivery_cost_back: 'Delivery pickup cost',
  Delivery_cost_go_back: 'Delivery Cost (return trip)',
  Rental_confirmation: 'Rental confirmation',
  Po_number_is_required: 'PO number is required',
  See_availability: 'See availability',
  Show_map_text: 'Show Map',
  About_us_label: 'About us',
  Under_construction_modal_text:
    "This section is under construction. We'll let you know as soon as it becomes available.",
  Under_construction_modal_title: 'Under construction',
  Under_construction_modal_button_text: 'Ok',
  Service_fees: 'Service fees',
  Sub_total: 'Sub total',
  Canceled_text: 'Canceled',
  MM_DD_YYYY: 'MM/DD/YYYY',
  Explore_all_equipments: 'Explore all equipment',
  Login_error_msg_company_spotlight:
    'If you log in from this page, you will lose your current search history',
  View_profile: 'View profile',
  Statistics_title: 'Analytics',
  Year: 'Year',
  Price_day: 'Price per day : ',
  Price_week: 'Price per week : ',
  Price_month: 'Price per month : ',
  Equipment_name_description: 'Name description',
  Tooler_bidz_spotlight_head_description_text_1:
    'Derental.bidz allows you to rent any equipment, any tool, any time. Tell us what you need and we’ll take care of the rest.',
  Tooler_bidz_spotlight_head_description_text_2:
    'Stop wasting hours on the phone or traveling to find out if the equipment you are looking for is available.',
  Tooler_bidz_spotlight_head_description_text_3:
    '1. Create your account on derentalequipment.com',
  Tooler_bidz_spotlight_head_description_text_4:
    '2. Fill in the criteria to identify the equipment you are looking for and submit your request.',
  Tooler_bidz_spotlight_head_description_text_5:
    '3. You will get a quote from all our partners with available inventory as soon as possible.',
  Tooler_bidz_spotlight_head_description_text_6: 'Simple, fast, efficient!',
  Not_applicable: 'Not applicable',
  Apply_filters: 'Apply filters',
  Equipment_price: 'Equipment price',
  Date_error_msg:
    'The end date should be equal or greater (>=) than the start date',
  User_exist: 'User already exist',
  Need_more_info_text: 'Call us',
  Explore_word: 'Explore our +500 locations',
  Rejected_text: 'Declined',
  SignUp_user_name_error:
    'Username cannot contain special characters or spaces ',
  Save_button: 'Save',
  Upload_img_msg: 'Picture update will take effect within 30min',
  Clear_search_box_text: 'Clear your search box',
  Submit_search_box_text: 'Submit your search',
  Submit_opening_hours_message: 'Save schedules',
  Open_pdf: 'Open PDF file',
  Download_pdf: 'Download PDF',
  Confirmation_under_30min: 'Confirmation under 30min',
  Back_label: 'Back ',
  Owner: 'Owner',
  Of: 'of',
  Dump_cart: 'Dump cart',
  Trash_chute: 'Trash chute',
  Are_you_sure_you_want_to_update_status:
    'Are you sure you want to update equipment status from booked to available ?',
  Select_bidz_equipments: 'Please select your bidz equipment',
  Select_equipments: 'Please select your equipment',
  Select_members: 'Please select your members',
  Select_CCF: 'Please select your Credit Check Application',
  Equipment_id: 'Equipment ID',
  Need_one: 'I Need an LDW',
  Have_one: 'I have a COI',
  Request_comment:
    'Please indicate any information that can help the rental center identify your need (reason for rental, use, etc.).',
  Assurance_renonciation_dommage: 'Damage waiver insurance',
  Assurance_renonciation_dommage_question:
    'Do you need an LDW or have a COI ? ',
  Insurance_company: 'Company insurance',
  Insurance_policy_number: 'Insurance policy number',
  Insurance_coverage: 'Insurance coverage',
  Expiry_date_of_insurance: 'Expiration date of insurance',
  Go_back: 'Round trip',
  Go: 'Drop',
  Back: 'Pick-up',
  Delivery_type: 'Delivery type',
  Insurance_booking_req_text_1:
    '* Damage waiver insurance is required to complete the booking of this equipment. A 9.96% fee is applicable. If you have such insurance, please provide the information in your customer portal to avoid paying its additional costs.',
  Insurance_booking_req_text_2:
    '* Delivery charges are applicable for the round trip if you have chosen the (Delivery) option in the delivery preferences. The delivery costs will be communicated to you later in the invoice.',
  Insurance_booking_req_text_3:
    '* Supply of accessories not included. Possibility to buy at the time of the transaction.',
  Upload_equipments_error_unable_to_get_equipments_from_airtable:
    '* Unable to get equipment from airtable',
  Upload_through_airtable: 'Add through airtable',
  Upload_selected_file: 'Upload excel file',
  Booked: 'Booked',
  Available_text: 'Available',
  Delete_equipment_button: 'Delete all equipment',
  Insurance_policy_number_required: 'Insurance policy number is required',
  Insurance_company_required: 'Insurance company is required',
  Insurance_coverage_required: 'Insurance coverage is required',
  Expiry_date_of_insurance_required: 'Expiry date of insurance is required',
  Comment: 'Comment',
  Delete_all_equipments_cta:
    'Are you sure you want to delete all your equipment ?',
  Space_error_msg:
    'Space is not allowed at the beginning or the end of the field',
  Professional: 'Professional',
  Individual: 'Individual',
  Name_must_be_at_least_3_characters: 'Name must be at least 3 characters',
  Unothorized: 'Unauthorized',
  Delete_all: 'Delete all',
  Upload_inventory: 'Upload inventory',
  Total_comments: 'Total comments',
  Avg_rating: 'Average rating',
  Drop: 'Pick-up',
  Pickup: 'Drop',
  Drop_and_pickup: 'Round trip',
  Yes: 'Yes',
  No: 'No',
  Logout_noun: 'Logout',
  Loading: 'Loading in progress',
  Cooking_equipment: 'Cooking equipment',
  Protable_hand_wash_stations: 'Portable hand wash stations',
  Portable_restrooms: 'Portable restrooms',
  Office_trailer: 'Office trailer',
  Barricade_and_sign: 'Barricade and sign',
  Roll_off_bins: 'Roll-off bins',
  Owner_text: 'Project Owner',
  Are_you_sure_you_want_to_sync: 'Are you sure you want to upload this file ?',
  Upload_equipments_error_unable_to_add_equipments_unable_to_get_airtable_equipments:
    'Unable to add equipment, please check the company name must be the same as the one in airtable then try again',
  Upload_equipments_error_unable_to_add_equipments_unable_to_set_equipment_with_id:
    'Unable to add equipment, please check that the equipment ID is unique in airtable and try again',
  Required_fields: 'You have to fill all required fields',
  Restroom_trailers: 'Restroom trailers',
  Shower_trailers: 'Shower trailers',
  Portable_shower: 'Portable shower',
  Jobsite_fences: 'Jobsite fences',
  Container_dumpster: 'Container dumpster',
  Container_recycling: 'Container recycling',
  Download_CFF_error:
    'You have to download CFF first through your management portal from the C.C.A section',
  Portable_hand_wash_stations: 'Portable hand wash stations',
  Select_a_project: 'Select a project',
  Select_a_credit_check_form: 'Select a Credit Check Application',
  Read_less: 'Read less',
  Read_more: 'Read more',
  Invalid_file_upload:
    'Something wrong ! File schema is inappropriate , please check it and try again .',
  Tool_security_box: 'Tool security box',
  SignUp_equipper_already_exists: 'Username already exists',
  SignUp_unable_to_signup_new_user: 'Email already exists',
  Apply_new_rental_dates: 'Update new rental period',
  No_projects_found: 'Make sure to add a project before a team member',
  Idle: 'Idle',
  Make_it_available: 'Make it available',
  Make_it_idle: 'Make it idle',
  Update_equipment_status_message:
    'Are you sure you want to update equipment status ?',
  Please_enter_a_value: 'Please enter a value',
  Are_you_sure_you_want_to_upload:
    'Are you sure you want to upload this file ?',
  Available_from: 'Available starting',
  Equipment_utility: 'Equipment purpose use :',
  Equipment_utility_placeholder:
    'Please let us know for each purpose you need the equipment for',
  Explore_step1: 'Explore primary fields ',
  Explore_step2: 'Explore secondary fields',
  Insurance_billing_summary: 'Insurance & Billing summary',
  Payment_delivery_details: 'Payment & Delivery details',
  Policy_details: 'Policy details',
  Become_lodger: 'Become a Renter',
  Get_connected: 'Get connected with us on social networks ',
  Links: 'Links',
  Explore_equipment: 'Explore equipment',
  Terms_and_conditions: 'Terms & conditions, ',
  Please_fill_all_fields: 'Please fill all fields',
  Tax_free_entity: 'Number of tax free years',
  Property_insurance: 'Property insurance',
  Required_project_number: 'Required project number',
  Utility_vehicule: 'Utility vehicule',
  Ascenseur: 'Elevator',
  Safety: 'Safety',
  Compactor: 'Compactor',
  Container: 'Container',
  Receiver_information_name: 'Main recipient - Name',
  Receiver_information_phone: 'Main recipient - Phone',
  Request_initiatior: 'Request initiator',
  Request_initiatior_name: 'Request initiator - Name',
  Request_initiatior_phone: 'Request initiator - Phone',
  Equipment_not_found_in: 'Equipment not found in',
  Discover_other_products_may_interset:
    'Discover other products may interest you',
  Send_bidz_request_no_equippers_found:
    'Sorry, we could not find any equipment that matches your request.',
  Please_fill_all_required_fields_before_submitting:
    'Please fill all required fields before submitting your request.',
  Something_went_wrong: 'Oops, Something went wrong',
  Something_went_wrong_description:
    'Retry again, if the problem persists please contact <NAME_EMAIL>',
  Upload_equipments_error_unable_to_add_equipments:
    'Please try again, connexion broken.',
  Brand_model: 'Brand model : ',
  Drive_type: 'Drive type : ',
  Diameter: 'Diameter : ',
  Cut_diameter: 'Cut diameter : ',
  Watt: 'Watt : ',
  CFM: 'CFM : ',
  Platform_height: 'Platform height : ',
  Working_height: 'Working height : ',
  Horizontal_outreach: 'Horizontal outreach : ',
  Platform_capacity: 'Platform capacity : ',
  Platform_dimension: 'Platform dimension : ',
  Platform_extension: 'Platform extension : ',
  Extension_capacity: 'Extension capacity : ',
  Platform_rotation: 'Platform rotation : ',
  Machine_rotation: 'Machine rotation : ',
  Machine_width: 'Machine width : ',
  Machine_length: 'Machine length : ',
  Machine_height: 'Machine height : ',
  Closed_machine_height: 'Closed machine height : ',
  Closed_machine_length: 'Closed machine length : ',
  Closed_machine_width: 'Closed machine width : ',
  Basket_capacity: 'Basket capacity : ',
  Basket_length: 'Basket length : ',
  Basket_width: 'Basket width : ',
  Legs_location: 'Legs location : ',
  Floor_height: 'Floor height : ',
  Cabin_height: 'Cabin height : ',
  Wheelbase: 'Wheelbase : ',
  Wheel_size: 'Wheel size : ',
  Plate_dimension: 'Plate dimension : ',
  Decibel: 'Decibel : ',
  Roll_width: 'Roll width : ',
  Compaction_equip_spec: 'Compaction : ',
  Vibrations: 'Vibrations : ',
  Lumen: 'Lumen : ',
  Pressure: 'Pressure : ',
  Frequency: 'Frequency : ',
  Tilting_capacity: 'Tilting capacity : ',
  Operation_capacity: 'Operation capacity : ',
  Tank_capacity: 'Tank capacity : ',
  Digging_depth: 'Digging depth : ',
  Dumping_height: 'Dumping height : ',
  Digging_radius: 'Digging radius : ',
  Technical_data_sheet: 'Add. specs : ',
  Equipment_usages: 'Equipment usages : ',
  Please_accept_the_terms_and_conditions:
    'Please accept the terms and conditions',
  Search: 'Search',
  Project: 'Project',
  Fax: 'Fax',
  Signature: 'Signature',
  Function_required: 'Function is required',
  Closed: 'Closed',
  Furniture: 'Furniture',
  Pressure_washer: 'Pressure washer',
  Equipment_not_found: 'Equipment not found',

  Applicant_info: 'Applicant information',
  Credit_references: 'Credit references',
  Date_of_birth: 'Date of birth',
  ExpirationID_date: 'Expiration ID date',
  State_formed: 'State formed',
  Tax_ID: 'Tax ID',
  Bank_name: 'Bank name',
  Bank_contact: 'Bank contact',
  Bank_phone: 'Bank phone',
  Partnership_agreement: 'Partnership agreement',
  Secondary_applicant_userID: 'Secondary applicant ID',
  Equipment_outside_us: 'Will the equipment be used outside of the U.S.?',
  Payments_non_domestic_location:
    'Will any payments be sent from a non-domestic location?',
  Operations_outside_us: 'Do you have operations outside of the U.S.?',
  Applicant_name: 'Applicant name',
  Expiration_ID_date: 'Expiration ID date',
  User_ID: 'ID',
  Occupation: 'Occupation',
  Role: 'Role',
  Type: 'Type',
  SSN: 'SSN',
  More_info: 'Additional information',
  President_email: 'President email',
  Attachments: 'Attachments',
  Bankruptcy:
    'Has the Applicant, Guarantor(s), or Principal(s) of the Applicant ever filed for bankruptcy?',
  Secondary_applicant_info: 'Secondary applicant information (If applicable)',
  Insurance: 'Insurance',
  View_file: 'View file',
  UserID: 'ID',
  Bankruptcy_equipment_info:
    'If the Applicant, Guarantor(s), or Principal(s) of the Applicant has ever filed for bankruptcy, please provide the details below',
  Condition: 'Condition',
  EquipmentManufacturerDescription: 'Equipment manufacturer description',
  Model: 'Model',
  Hours: 'Hours',
  Serial: 'Serial',
  SalesPrice: 'Sales price',
  Bankruptcy_trade_info:
    'If the Applicant, Guarantor(s), or Principal(s) of the Applicant has ever filed for bankruptcy, please provide the details below',
  Trade_in_year: 'Trade In Year',
  TradeInModel: 'Model',
  TradeInHours: 'Trade In Hours',
  TradeInSerial: 'Serial',
  TradeInAllowance: 'Allowance',
  Amount_owed_on_trade_in: 'Amount owed on trade-in',
  Net_trade_in: 'Net trade-in',
  Owed_to_account_number: 'Owed to account number',
  Bankruptcy_cashDown_info:
    'If the Applicant, Guarantor(s), or Principal(s) of the Applicant has ever filed for bankruptcy, please provide the details below',
  Cash_down_program_number: 'Cash down program number',
  Cash_down_program_description: 'Cash down program description',
  Cash_down_effective_date: 'Cash down effective date',
  Cash_down_interest_start_date: 'Cash down interest start date',
  Cash_down_first_payment_date: 'Cash down first payment date',
  Cash_down_term: 'Cash down term',
  Cash_down_frequency: 'Cash down frequency',
  Estimated_amount_financed: 'Estimated amount financed',
  Second_applicant: 'Second applicant',
  No_file_uploaded: 'No file uploaded',
  Terms_caf_us:
    'I certify that the information stated in this application is true and correct. I understand that you will retain this application whether or not it is approved . You and /or your assigns or prospective assigns are authorized to check my credit (including credit bureau reports) and employment history, obtain insurance information and to answer questions about your credit experience with me. I authorize you (i) to contact my creditors and authorize any creditor so contact to release to you such credit information as you may request and (ii) to share this application and my financial information with your employees and other representatives who are involved in the evaluation of my application, including syndication parties and resource providers. PATRIOT ACT Notice: To help the government fight the funding of terrorism and money laundering activities, U.S. Federal law requires financial institutions to obtain, verify and record information that identifies each person (individuals or business) who opens an account. When you apply to open an account or to add any additional service, we will ask you for your name, address, and taxpayer ID number and other information that will allow us to identify you. We may also ask to see other identifying documents. Please note: The signatures below also apply to the validity of Trade References listed on the back of this application. ',
  Terms_sales_us:
    'TERMS OF SALE, INCLUDING TERMS OF PAYMENT AND CHARGES,FOR EACH PURCHASE ARE AGREED TO BE THOSE SPECIFIED ON THE FACE OF EACH INVOICE. THE CUSTOMER HEREBY AGREES TO PAY ALL COSTS OF COLLECTION OR LEGAL FEES SHOULD SUCH ACTION BE NECESSARY DUE TO NON-PAYMENT. THE ABOVE INFORMATION IS WILLINGLY SUPPLIED AND THE CREDITOR IS AUTHORIZED TO CONTACT THE ABOVE BANK AND TRADE REFERENCES IN ORDER TO ESTABLISH THE CREDITWORTHINESS OF THE ABOVE NAMED COMPANY. IF THE APPLICANT IS NOT A CORPORATION, THE CREDITOR IS AUTHORIZED TO OBTAIN CREDIT REPORTS ON THE PROPRIETORS, PARTNERS OR PRINCIPALS. SHOULD A CREDIT AVAILABILITY BE GRANTED BY THE CREDITOR, ALL DECISIONS WITH RESPECT TO THE EXTENSION OR CONTINUATION SHALL BE IN THE SOLE DISCRETION OF THE CREDITOR. THE CREDITOR MAY TERMINATE ANY CREDIT AVAILABILITY WITHIN ITS SOLE DISCRETION.PAYMENT TERMS ARE NET 30 DAYS. BECAUSE OF THE ADDITIONAL EXPENSE TO US IN HANDLING DELINQUENT ACCOUNTS, A SERVICE CHARGE OF 1-1/2% PER MONTH (18%PER ANNUM) WILL BE CHARGED ON PAST DUE ACCOUNTS.',
  Tax_ID_required: 'Tax ID is required',
  Has_other_accounts: 'Has other accounts ?',
  Used: 'Used',
  New: 'New',
  Corporation: 'Corporation',
  Partnership: 'Partnership',
  Limited_Liability_Company: 'Limited Liability Company',
  Limited_Liability_Partnership: 'Limited Liability Partnership',
  Municipality: 'Municipality',
  Drivers_License: "Driver's License",
  Passport: 'Passport',
  Other_government_issued_ID: 'Other government issued ID',
  Fulltime_farmer: 'Full-time Farmer',
  Building_contractor: 'Building Contractor',
  Road_street: 'Road & Street',
  Rental_yard: 'Rental Yard',
  Parttime_farmer: 'Part-time Farmer',
  Excavating_trenching: 'Excavating/Trenching',
  Construction: 'Construction',
  Logging: 'Logging',
  Custom_operator: 'Custom Operator',
  Lawn_landscape: 'Lawn & Landscape',
  Non_ag_business_purposes: 'Non AG business purposes',
  Coapplicant: 'Co-applicant',
  Guarantor: 'Guarantor',
  Officer: 'Officer',
  Partner: 'Partner',
  Under_construction_tab: 'Under construction',
  Username_tooltip: 'Username is unique and cannot be changed',
  Categories_tooltip: 'Select the categories in which you offer rentals.',
  Usage: 'Usage',
  CE: 'CE',
  AG: 'AG',
  Renter_details: 'Renter details',
  Equipement_home: 'Equipment rentals made easier',
  Equipement_text_home:
    'Welcome to the future of equipment rental. Our platform simplifies the process, making it effortless to find, book, and manage the equipment you need.',
  Book_demo: 'Book a demo',
  Explore_home: 'Explore our equipment',
  Text_home: 'Browse by category to find exactly what you are looking for',
  Explore_derental_catalog: 'Explore Derental catalog',
  Explore_equipper_catalog: 'Explore Equipper catalog',
  Form_footer_h: 'Our team is available 24/7 to assist you',
  Form_footer_text: 'You will receive a call from our team in no time',
  Need_more_info_text_social: 'Connect with us',
  Text_footer_bottom:
    'Digitize your inventory by adding your equipment to Derental. Gain access to new customers and drive more adoption and revenue.',
  Reach_out: 'Interested in working with us?',
  Reach_out_button: 'Just reach out',
  Explore_rentals: 'Discover Our Featured Vendors',
  Equipment_information: 'Equipment information',
  Technical_information: 'Technical information',
  Usage_information: 'Usage information',
  Reservation_details: 'Reservation details',
  Partnering_with: 'Partnering with',
  Contractors_who_trust_us: 'Contractors who trust us',
  Special_note: 'Special note',
  Select: 'Select',
  Your_note: 'Your note',
  Has_other_accounts_placeholder: ' Enter your other account',
  Property_insurance_placeholder: 'Enter your property insurance',
  Required_project_number_placeholder: 'Enter your project number',
  Tax_free_entity_placeholder: 'Enter your tax free entity',
  Invalid_birth_date: 'Invalid birth date',
  Invalid_expiration_date: 'Invalid expiration date',
  Allowance: 'Allowance',
  Invalid_hours: 'Invalid hours',
  Invalid_year: 'Invalid year',
  Upload_equipments_error_unable_to_add_tooler_inventory:
    'Connection broken, please try again',
  Specify_the_specifications_of_the_equipment:
    'Please add some of the specs of your equipment for better finding results',
  Specafication: 'Specification',
  Specafications: 'Specifications',
  Value: 'Value',
  Add_pecafication: 'Add specification',
  Specify_the_propulsion_method_used_by_your_equipment:
    'Specify the propulsion method used by your equipment to enhance search results',
  This_will_help_us_find_the_right_match_for_your_needs:
    'This will help us find the right match for your needs',
  Our_automated_bidding_system_DerentalBidz_will_request:
    'Our automated bidding system Derental.Bidz, will request quotations for you by email from all rental centers near your jobsite. We make sure you find what you looking for, that guaranteed.!',
  It_that_simple_fast_and_efficient: 'It’s that simple, fast and efficient!',
  You_will_receive_a_quote_from_the_rental_centers_near_your_jobsite:
    'You will receive a quote from the rental centers near your jobsite',
  Fill_in_the_criteria_to_identify_the_equipment_you_need_and_submit_your_request:
    'Fill in the criteria to identify the equipment you need and submit your request',
  Create_your_account_on: 'Create your account on',
  Follow_these_steps_to_get_started: 'Follow these steps to get started',
  No_more_wasting_hours_on_the_phone_or_traveling_to_check_if_the_equipment_you_are_looking_for_is_available:
    'No more wasting hours on the phone or traveling to check if the equipment you are looking for is available',
  Tell_us_what_you_need_and_well_take_care_of_the_rest:
    'Tell us what you need and we’ll take care of the rest',
  Derentalbidz_allows_you_to_rent_any_equipment_any_tool_any_time:
    'Derental.bidz allows you to rent any equipment, any tool, any time',
  Let_us_find_you_exactly_what_you_are_looking_for:
    'Let us find you exactly what you are looking for',
  Choose_a_specification: 'Choose a specification',
  Add_specification: 'Add specification',
  Go_back_to_home: 'Go back to home page',
  Equipment_purpose_use: 'Equipment purpose use',
  Related_products: 'Related products',
  Explore_footer: 'Explore',
  Choose_a_propulsion: 'Choose a propulsion',
  Specification: 'Specification',
  Bidz_allow_you_to_rent_any_equipment_any_tool_any_time:
    'Bidz allows you to rent any equipment, any tool, any time. Tell us what you need and we’ll take care of the rest.',
  Let_us_find_your_equipment: 'Let’s find your equipment',
  Optional: 'Optional',
  Mission: 'Mission',
  Vision: 'Vision',
  Mission_description:
    ' Our goal is to innovate the equipment rental industry without drastically changing the way things are done, taking baby steps to move the industry towards a fully automated process.',
  Vision_description:
    'We are in business to help the digital shift of the equipment rentals industry for the digital generation who are now the decision makers.  ',
  tools_heavy_equipment_rental_process:
    'tools & heavy equipment rental process',
  Revolutionizing: 'Revolutionizing',
  Founding_team: 'Founding Team',
  Founding_team_description:
    ' Our team has spent years on large-scale products delivery for major tech companies like CGI and Unity. We deeply understand complex software, understanding human language, adapting technology to ground work needs and making programs that our customers & partners can use. ',
  Backed_by: 'Backed By',
  Through_the_tools_that_we_build:
    ' Through the tools that we build, we want to push the world to create better products and services.',
  Booking_conditions: 'Booking conditions',
  Insurance_and_billing_summary: 'Equipment Protection Plan',
  WillCall_name: 'Will-Call',
  Pick_up_name_us: 'Will-Call',
  Assurance_renonciation_dommage_question_us: 'Do you have an LDW ?',
  Assurance_renonciation_dommage_us: 'Limited Damage Waiver',
  I_have_read_and_agree_to_the_booking_policy: 'I have read and agree to the  ',
  Booking_policy: 'Booking policy  ',
  Insurance_booking_req_text_4:
    'The delivery costs will be communicated to you later separately, by your field representative.',
  Insurance_booking_req_text_2_us:
    'Delivery charges are applicable for the round trip if you have chosen the (Delivery) option in the delivery preferences.',
  Tax_rate: 'Tax rate',
  Delivery_name_us: 'Delivery',
  Electric: 'Electric',
  Diesel: 'Diesel',
  Gas: 'Gas',
  Propane: 'Propane',
  Manual: 'Manual',
  Hydraulic: 'Hydraulic',
  Battery: 'Battery',
  Hybrid: 'Hybrid',
  Natural_gas: 'Natural gas',
  Air: 'Air',
  No_result: 'No equipment found',
  Projects: 'Project(s)',
  In_charge_of_rentals: 'In charge of rentals',
  Accounts_payable: 'Accounts payable',
  Members: 'Member(s)',
  Quota_details: 'Quota details',
  Lets_find_your_equipment: 'Let’s find your equipment',
  Please_choose_the_method_of_upload: 'Please choose the method of upload',
  Excavator_Accessory: 'Excavator Accessory',
  Accessory: 'Accessory',
  Elevator: 'Elevator',
  Ratchet: 'Ratchet',
  Snow: 'Snow',
  Flooring: 'Flooring',
  Tank: 'Tank',
  Storage: 'Storage',
  Electronic: 'Electronic',
  Contact_us: 'Contact us',
  Land_clearing: 'Land clearing',
  Make_sure_to_book_an_equipment:
    "Oops! It looks like you haven't booked any equipment yet",
  Make_your_first_booking: 'Make your first booking',
  Select_lodgers: 'Select renters',
  Select_discount: 'Select discount',
  Apply_promotions_to_lodgers: 'Apply promotions to renters',
  Are_you_sure_you_want_to_delete_this_promotion:
    'Are you sure you want to delete this promotion ?',
  Delete_promotion: 'Delete promotion',
  Renters: 'Renters',
  Discounts: 'Discounts',
  No_promotions_found: 'No applied promotion(s)',
  You_have_no_requests: 'You have no requests',
  This_total_amount_excludes_tax:
    ' This total amount excludes tax and ancillary charges',
  Signup_equipper_btn: 'Start renting your equipment',
  Number_of_employees_must_be_positive: 'Number of employees must be positive',
  Failed_to_check_member: 'Expired invitation. Unable to join the team.',
  Advisory_board: 'Advisory Board',
  Rental_duration: 'Rental duration',
  And: 'and',
  Emails_notification_list: 'Emails notification list',
  Recipient_list_placeholder:
    'Enter valid email addresses, separated by COMMA or ENTER.',
  Minimum_rental_period: 'Minimum rental period',
  Enter_minimum_rental_duration: 'Enter minimum rental duration (days)',
  Equipment_names_select_info: '(MRP) will be applied to selected options',
  Apply_changes: 'Apply changes',
  MRP_names_select_info:
    'To disable this feature, simply set the minimum rental duration to 0.',
  Success_excel_upload:
    'Your file has successfully been uploaded. Please note that you will receive a report by email if the system encountered issues uploading any equipment.',
  Rows_per_page: 'Rows per page :',
  Status_price: 'Status & Price',
  Image_equipment_recommended:
    'This is a standard image we recommend. Feel free to change it.',
  Please_fill_in_fields: 'Please fill in all the necessary fields',
  Next: 'Next',
  Submit: 'Submit',
  Equipment_picture: 'Equipment picture',
  Equipment_successfully_added: 'Equipment Successfully Added',
  Add_single_equipment: 'Add single equipment',
  Price_four_week: 'Price per 4 weeks : ',
  Internal_id_required: 'Equipment ID is required',
  Price_required: 'Price is required',
  Equipment_name_required: 'Equipment Name is required',
  Price_positive: 'Must be a positive number',
  Specifications_required: 'Specifications are required',
  Specifications_value_required: 'Value is required',
  Equipment_not_added: 'Equipment not added',
  Internal_id: 'Equipment ID',
  Back_to_equipment: 'Back to Equipment Management',
  Equipment_not_available:
    'Equipment is not available? Please, <1>contact us</1> to resolve it.',
  Location_not_available:
    'Location is not available? Please, <1>contact us</1> to resolve it.',
  Description_fr: 'Description',
  Choose_your_equipment_first: 'Choose your equipment first',
  Minimum_rental_period_msg: 'Rental period must be at least ',
  Drop_hour: 'Drop hour',
  Pickup_hour: 'Pickup hour',
  Drop_pickup_hour: 'Pickup & Drop hours',
  Flash_message_text_stripe:
    'Some requests might take up to a few minutes to appear on the manage requests tab due to payment processors delays.',
  Back_btn: 'Back',
  Image_equipment_update: 'No custom image? A standard one will be shown.',
  Minimum_rental_period_must_be_greater_than_0:
    'Rental period must be at least  1 day',
  Choose_file: 'Choose file',
  Invalid_emails_found: 'Invalid email(s) found',
  Your_own_email_cannot_be_added: 'Your own email cannot be added',
  Flash_message_update_equipment_status:
    'After each action, equipment may take a few seconds to update. Please refresh the page.',
  Copied: 'Copied ! ',
  Invalid_internal_id: "Invalid ID : Equipment ID can't include '|||' ",
  Unable_to_find_user_by_email: 'Unable to find user by email',
  Equipment_found: 'equipment found',
  You_cant_delete_booked_equipment: 'You cannot delete booked equipment.',
  Preferred_equipment_name: 'Equipment Name (preferred)',
  Sales_price_must_be_greater_than_0: 'Sales price must be greater than 0',
  Assigned_projects: 'Assigned Project(s)',
  Value_must_be_a_positive_number: 'Value must be a positive number',
  Preferred_equipment_name_error:
    'The name you entered is already used as the equipment name or one of its alternative names (aliases). Please choose a different name.',
  Book_equipment_error_equipper_is_closed_during_the_requested_booking_dates:
    'Equipper is closed during the requested booking dates',
  Alternative_name: 'Alternative name',
  You_need_to_sign_in_as_a_renter: 'You need to sign in as a renter',
  Access_not_available: 'Access Not Available',
  Feature_not_accessible_in_region:
    'This feature is not accessible in your country/region.'
};
