import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import * as Yup from 'yup';
import DatePickerComponent from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import DatePicker from '../date_picker/Date_picker';
import EquipmentsModal from './Equipments_modal';
import RenderIf from '../Render_if';
import {
  address,
  hideDeliveryAddress,
  showDeliveryAddress
} from '../../helpers/Address_helper';
import {
  insurance,
  resetInsurance,
  calculateDurationInDays
} from '../../helpers/Data_helper';
import {
  deliveryAddressSchema,
  insuranceSchema
} from '../../helpers/Validation_schema';
import CustomButton from '../buttons/Custom_button';
import CustomLabel from '../labels/Custom_label';
import GeoLocation from '../inputs/Geonames';
import { PHONE_NUMBER_REGEX } from '../../helpers/Regex';
import Insurance from '../inputs/Insurance';
import Modal from './Modal';
import FormikForm from '../forms/Formik';
import Input from '../forms/Input';
import { PhoneInputField } from '../inputs/Phone_input_field';
import MultiSelect from '../multi_select/Multi_select';
import CheckboxInput from '../inputs/Checkbox';
import { ErrorMessage, Field } from 'formik';
import useResponsive from '../../helpers/Responsive';
import { getCookies } from '../../helpers/Cookies';
import { isDisabled } from '../../helpers/Date_helper';

export default function RequestConfirmationModal({
  data,
  t,
  show,
  isLoading,
  promotion,
  title,
  handleClose,
  hoursPicker,
  creditCheckForms,
  projects,
  detectLanguage,
  onSubmitFn,
  isBooking,
  isBidz,
  textButton
}) {
  const [requestData, setRequestData] = useState(data);
  const [showInfo, setShowInfo] = useState(false);

  const startDate = sessionStorage.getItem('start_date');
  const endDate = sessionStorage.getItem('end_date');
  const { isMobile } = useResponsive();

  const initialValues = {
    start_date: startDate ? new Date(parseInt(startDate)) : new Date(),
    end_date: endDate ? new Date(parseInt(endDate)) : new Date(),
    datePicker_validator: false,
    insurance: {
      insurance_coverage: '',
      expiry_date_of_insurance: '',
      insurance_company: '',
      insurance_policy_number: ''
    },
    phone_number: data?.phone_number,
    minimum_rental_period: data?.minimum_rental_period,

    receiver_info: {
      name: '',
      phone: ''
    },
    pickup_time: {
      drop: '',
      pickup: '',
      pickup_time_validator: false
    },
    policy: false,
    company: data?.company,
    lodger_name: data?.full_name,
    isCreditAccount: false,
    need_one: false,
    isDelivery: false,
    project: null,
    credit_check_form: null,
    po_number: '',
    accept_terms: false,
    payment_method: '',
    comment: '',
    delivery_type: 'pickup',
    have_one: false,
    ...address
  };

  const isUS = getCookies('country') === 'United States';
  const validationSchema = Yup.object().shape({
    insurance: Yup.object().when('need_one', {
      is: true,
      then: insuranceSchema(t)
    }),
    receiver_info: Yup.object().shape({
      name: Yup.string().required(t('Name_required')),
      phone: Yup.string()
        .required(t('Phone_number_required'))
        .matches(PHONE_NUMBER_REGEX, t('Invalid_phone_number'))
    }),
    credit_check_form: Yup.object()
      .nullable()
      .when('isCreditAccount', {
        is: true,
        then: Yup.object().required(t('This_field_is_required')).nullable()
      }),

    delivery_address: Yup.object().when('isDelivery', {
      is: true,
      then: deliveryAddressSchema(t)
    }),
    accept_terms: Yup.boolean().oneOf(
      [true],
      t('Please_accept_the_terms_and_conditions')
    ),
    payment_method: Yup.object()
      .required(t('This_field_is_required'))
      .nullable(),
    datePicker_validator: Yup.boolean().oneOf(
      [false],
      'The end date should be equal or greater (>=) than the start date'
    ),
    pickup_time: Yup.object().shape({
      drop: Yup.date().required(t('This_field_is_required')).nullable(),
      pickup: Yup.date().required(t('This_field_is_required')).nullable(),
      pickup_time_validator: Yup.boolean().oneOf(
        [false],
        'Drop time must be greater than Pickup time, since the rental period is less than 24 hours.'
      )
    })
  });

  const handleChangeSelectedCCA = (value, formik) => {
    if (value) {
      onChange(value, 'credit_check_form', formik);
      onChange(value.value.company.company_name, 'company', formik);
    } else {
      onChange(null, 'credit_check_form', formik);
      onChange(data.company, 'company', formik);
    }
  };

  const isDropTimeValid = (pickup, startDate, endDate, drop) => {
    if (startDate.setHours(0, 0, 0, 0) === endDate.setHours(0, 0, 0, 0)) {
      return drop < pickup;
    }
    return false;
  };

  const onChange = (value, name, formik) => {
    formik?.setFieldValue(name, value);
  };

  const handleChangeSelectedProject = (value, formik) => {
    if (value) {
      formik?.setFieldValue('project', {
        ...value.value,
        label: value.value.name,
        value: value.value.id
      });
      if (formik?.values.isDelivery) {
        formik?.setFieldValue('delivery_address', {
          ...value.value.delivery_address,
          city: {
            label: value.value.delivery_address.city,
            value: value.value.delivery_address.city
          },
          country: {
            label: value.value.delivery_address.country,
            value: value.value.delivery_address.country
          },
          state: {
            label: value.value.delivery_address.state,
            value: value.value.delivery_address.state
          }
        });
      }
      if (value.value.credit_check_form?.id) {
        formik?.setFieldValue('credit_check_form', {
          ...value.value?.credit_check_form,
          label: value.value?.credit_check_form?.name.replaceAll('_', ' '),
          value: value.value?.credit_check_form
        });
        formik?.setFieldValue(
          'company',
          value.value.credit_check_form?.company?.company_name
        );
      }
    } else {
      formik?.setFieldValue('project', null);
      formik.setFieldValue('delivery_address', {
        address: '',
        state: '',
        zip_code: '',
        city: '',
        country: ''
      });
      formik?.setFieldValue('credit_check_form', null);
      formik?.setFieldValue('company', data?.company);
    }
  };
  const currentTime = new Date();

  const handleStartDateChange = (date, formik) => {
    date.setHours(
      currentTime.getHours(),
      currentTime.getMinutes(),
      currentTime.getSeconds(),
      currentTime.getMilliseconds()
    );
    setRequestData({
      ...requestData,
      start_date: date
    });
    formik?.setFieldValue('start_date', date);
    if (date > formik?.values?.end_date) {
      formik?.setFieldValue(
        'datePicker_validator',
        isDisabled(date, formik.values.end_date)
      );
    } else {
      formik?.setFieldValue('datePicker_validator', false);
    }
    if (
      date.setHours(0, 0, 0, 0) ===
      formik?.values?.end_date.setHours(0, 0, 0, 0)
    ) {
      formik?.setFieldValue(
        'pickup_time.pickup_time_validator',
        isDropTimeValid(
          formik.values.pickup_time.pickup,
          date,
          formik.values.end_date,
          formik.values.pickup_time.drop
        )
      );
    } else {
      formik?.setFieldValue('pickup_time.pickup_time_validator', false);
    }
  };

  const handleEndDateChange = (date, formik) => {
    date.setHours(
      currentTime.getHours(),
      currentTime.getMinutes(),
      currentTime.getSeconds(),
      currentTime.getMilliseconds()
    );
    setRequestData({
      ...requestData,
      end_date: date
    });
    formik?.setFieldValue('end_date', date);

    if (formik.values.start_date === new Date(parseInt(startDate))) {
      formik.setFieldValue('start_date', new Date(parseInt(startDate)));
    }
    if (
      date.setHours(0, 0, 0, 0) <
      formik?.values?.start_date.setHours(0, 0, 0, 0)
    ) {
      formik?.setFieldValue(
        'datePicker_validator',
        isDisabled(formik.values.start_date, date)
      );
    } else {
      formik?.setFieldValue('datePicker_validator', false);
    }

    if (
      date.setHours(0, 0, 0, 0) ===
      formik?.values?.start_date.setHours(0, 0, 0, 0)
    ) {
      formik?.setFieldValue(
        'pickup_time.pickup_time_validator',
        isDropTimeValid(
          formik.values.pickup_time.pickup,
          formik.values.start_date,
          date,
          formik.values.pickup_time.drop
        )
      );
    } else {
      formik?.setFieldValue('pickup_time.pickup_time_validator', false);
    }
  };

  const handleShow = (formik) => {
    formik?.setFieldValue('need_one', !formik?.values.need_one);
    setRequestData({
      ...requestData,
      need_one: !formik?.values.need_one
    });
    if (!formik?.values.need_one) {
      resetInsurance(formik);
    }
  };

  const handleShowInfo = () => {
    setShowInfo(!showInfo);
  };

  const handleDelivery = (formik) => {
    if (formik?.values.isDelivery) {
      hideDeliveryAddress(formik);
    } else {
      showDeliveryAddress(formik);
    }
  };

  const handleDeliveryType = (event, formik) => {
    formik?.setFieldValue('delivery_type', event.target.id);
  };

  useEffect(() => {
    const date = new Date();

    setRequestData({
      ...requestData,
      start_date: startDate
        ? new Date(parseInt(startDate)).setHours(
            date.getHours(),
            date.getMinutes(),
            date.getSeconds(),
            date.getMilliseconds()
          )
        : new Date(),
      end_date: endDate
        ? new Date(parseInt(endDate)).setHours(
            date.getHours(),
            date.getMinutes(),
            date.getSeconds(),
            date.getMilliseconds()
          )
        : new Date(date.setDate(date.getDate() + 6)),
      need_one: false
    });
  }, [data]);

  const getTimeFromStr = (timeStr) => {
    const [hours, minutes] = timeStr.split(':').map(Number);
    const date = new Date();
    date.setHours(hours);
    date.setMinutes(minutes);
    date.setSeconds(0);
    return date;
  };

  const getOpenCloseTimes = (dayOfWeek) => {
    const dayInfo = hoursPicker?.find((day) => day.day_of_week === dayOfWeek);
    if (!dayInfo || dayInfo.day_off) {
      return { minTime: null, maxTime: null };
    }
    return {
      minTime: getTimeFromStr(dayInfo.open),
      maxTime: getTimeFromStr(dayInfo.close)
    };
  };

  const getClosedDays = () => {
    if (!hoursPicker) return [];
    const closedDays = hoursPicker.filter((day) => day.day_off === true);
    return closedDays.map((day) => day.day_of_week);
  };

  useEffect(() => {
    return () => {
      setRequestData(null); // Clean up state to prevent memory leaks
    };
  }, []);

  const getDisabledDates = () => {
    const closedDays = getClosedDays();
    const today = new Date();
    const disabledDates = [];

    // Loop through the next 12 months
    for (let monthOffset = 0; monthOffset < 12; monthOffset++) {
      const monthDate = new Date(
        today.getFullYear(),
        today.getMonth() + monthOffset
      );

      closedDays.forEach((day) => {
        const dayIndex = [
          'Sunday',
          'Monday',
          'Tuesday',
          'Wednesday',
          'Thursday',
          'Friday',
          'Saturday'
        ].indexOf(day);
        const closedDate = new Date(monthDate);
        closedDate.setDate(1); // Start from the first of the month
        // Get the first occurrence of the closed day in this month
        closedDate.setDate(((dayIndex - closedDate.getDay() + 7) % 7) + 1);

        // Keep adding occurrences for this month
        while (closedDate.getMonth() === monthDate.getMonth()) {
          disabledDates.push(closedDate.toISOString().split('T')[0]); // Format date as YYYY-MM-DD
          closedDate.setDate(closedDate.getDate() + 7); // Move to the next occurrence
        }
      });
    }

    return disabledDates;
  };

  const disabledDates = getDisabledDates();

  const isDateDisabled = (date) => {
    return !disabledDates.some(
      (disabledDate) => date?.toISOString().split('T')[0] === disabledDate
    );
  };

  const RequestForm = ({ formik }) => {
    const dropHours = getOpenCloseTimes(
      formik.values.end_date?.toLocaleDateString('en-US', {
        weekday: 'long'
      })
    );

    const pickupHours = getOpenCloseTimes(
      formik.values.start_date?.toLocaleDateString('en-US', { weekday: 'long' })
    );

    const isDisabled =
      formik.values.minimum_rental_period >
      calculateDurationInDays(
        formik.values.start_date,
        formik.values.end_date
      ) +
        1;

    return (
      <div className="infos-equipments w-100">
        <div className="row">
          <div className="col-lg-6">
            <div className="row align-items-center justify-content-between row-infos form-group">
              <div className="col-lg-12 padding-r-0 padding-l-0">
                <div className="margin-top-10 relative">
                  <Input
                    name="lodger_name"
                    disabled
                    isNotRequired
                    formikTouched={formik?.touched}
                    formikErrors={formik?.errors}
                    label={t('Request_initiatior_name')}
                  />
                </div>

                <div className="margin-top-10">
                  <Input
                    name="phone_number"
                    component={PhoneInputField}
                    disabled
                    isNotRequired
                    formikTouched={formik?.touched}
                    formikErrors={formik?.errors}
                    label={t('Request_initiatior_phone')}
                  />
                </div>
              </div>
            </div>
            <div className="row align-items-center justify-content-between row-infos form-group">
              <div className="col-lg-12 padding-r-0 padding-l-0 ">
                <div className="margin-top-10 relative">
                  <Input
                    name="receiver_info.name"
                    type="text"
                    placeholder={t('Name')}
                    formikTouched={formik?.touched}
                    formikErrors={formik?.errors}
                    label={t('Receiver_information_name')}
                  />
                </div>
                <div className="margin-top-10 relative">
                  <Input
                    name="receiver_info.phone"
                    component={PhoneInputField}
                    placeholder={t('Phone_number')}
                    formikTouched={formik?.touched}
                    formikErrors={formik?.errors}
                    label={t('Receiver_information_phone')}
                  />
                </div>
              </div>
            </div>
            <div className="row align-items-center justify-content-between row-infos form-group">
              <div className="col-lg-12 padding-r-0 padding-l-0 ">
                <div className="margin-top-10 relative">
                  {data.type === 'pro' && (
                    <Input
                      name="company"
                      disabled
                      label={t('Company_name')}
                      formikTouched={formik?.touched}
                      formikErrors={formik?.errors}
                      isNotRequired
                    />
                  )}
                </div>
              </div>
            </div>
            <div className="row align-items-center justify-content-between row-infos form-group">
              <div className="col-lg-12 padding-r-0 padding-l-0">
                <div className="margin-top-10 relative">
                  {data?.type === 'pro' && (
                    <Input
                      name="project"
                      type="text"
                      formikTouched={formik?.touched}
                      formikErrors={formik?.errors}
                      isNotRequired
                      component={() => (
                        <MultiSelect
                          options={projects || []}
                          isGeoLocation
                          t={t}
                          handleChange={(value) =>
                            handleChangeSelectedProject(value, formik)
                          }
                          name="project"
                          value={formik?.values.project}
                          placeholder={t('Select')}
                        />
                      )}
                      label={t('Assign_to_project')}
                    />
                  )}
                </div>
              </div>
            </div>
            <div className="row align-items-center justify-content-between row-infos form-group">
              <div className="col-lg-12 padding-r-0 padding-l-0">
                <div className="margin-top-10 relative">
                  <Input
                    name="credit_check_form"
                    type="text"
                    formikTouched={formik?.touched}
                    formikErrors={formik?.errors}
                    isNotRequired={!formik.values.isCreditAccount}
                    component={() => (
                      <MultiSelect
                        options={creditCheckForms || []}
                        isGeoLocation
                        t={t}
                        disabled={
                          formik?.values?.project?.credit_check_form?.id
                        }
                        handleChange={(value) => {
                          handleChangeSelectedCCA(value, formik);
                        }}
                        name="credit_check_form"
                        value={formik?.values.credit_check_form}
                        placeholder={t('Select')}
                      />
                    )}
                    label={t('Credit_check_form')}
                  />
                </div>
              </div>
            </div>
                        <div className="row align-items-center justify-content-between row-infos margin-top-10">
              <div className="col-lg-12 padding-r-0 padding-l-0">
                <label className="t-body-regular c-fake-black">
                  {t('Special_note')}
                </label>
                <Field
                  as="textarea"
                  id="comment"
                  name="comment"
                  className="form-control w-100 h-90 margin-top-10 pad-top-10"
                  rows="4"
                  placeholder={t('Your_note')}
                  cols="50"
                />
                <ErrorMessage name="comment" component="span" />
              </div>
            </div>
            <RenderIf condition={!isMobile}>
              <div className="d-flex label-check align-items-center marg-top-20 d-lg-flex d-none">
                <CheckboxInput
                  label={
                    <>
                      {t('Accept')}
                      <span
                        onClick={handleShowInfo}
                        className="c-red ml-1 d-inline-block bold underline-red"
                      >
                        {t('Booking_conditions')}{' '}
                      </span>
                    </>
                  }
                  name="accept_terms"
                  id="accept_terms"
                  onChange={() =>
                    formik?.setFieldValue(
                      'accept_terms',
                      !formik?.values.accept_terms
                    )
                  }
                  checked={formik?.values.accept_terms}
                />
              </div>
              <ErrorMessage
                name="accept_terms"
                component="span"
                className="error-message"
              />
            </RenderIf>
          </div>
          
          <div className="col-lg-6">
            <div className="row align-items-center justify-content-between row-infos form-group">
              <div className="col-lg-12 padding-r-0 padding-l-0">
                <div className="margin-top-10 relative">
                  <label className="t-body-regular c-fake-black">
                    {t('Delivery_preference')}
                  </label>
                  <div className="d-flex">
                    <CheckboxInput
                      label={t('Pick_up_name')}
                      name="radio-group"
                      style="mr-2"
                      checked={!formik?.values.isDelivery}
                      onChange={() => handleDelivery(formik)}
                      id="pick-up"
                    />
                    <CheckboxInput
                      label={t('Delivery_name')}
                      name="radio-group"
                      style="mr-2"
                      checked={formik?.values.isDelivery}
                      onChange={() => handleDelivery(formik)}
                      id="delivery"
                    />
                  </div>
                </div>
              </div>
            </div>
            <RenderIf condition={formik?.values.isDelivery}>
              <div className="row align-items-center justify-content-between row-infos form-group">
                <div className="col-lg-12 padding-r-0 padding-l-0">
                  <div className="margin-top-10 relative">
                    <div className="d-flex">
                      <CheckboxInput
                        label={t('Go')}
                        name="delivery-type"
                        style="mr-2"
                        checked={formik?.values.delivery_type === 'pickup'}
                        defaultChecked
                        onChange={(event) => handleDeliveryType(event, formik)}
                        id="pickup"
                      />
                      <CheckboxInput
                        label={t('Back')}
                        name="delivery-type"
                        style="mr-2"
                        checked={formik?.values.delivery_type === 'drop'}
                        onChange={(event) => handleDeliveryType(event, formik)}
                        id="drop"
                      />
                      <CheckboxInput
                        label={t('Go_back')}
                        name="delivery-type"
                        style="mr-2"
                        checked={
                          formik?.values.delivery_type === 'drop_and_pickup'
                        }
                        onChange={(event) => handleDeliveryType(event, formik)}
                        id="drop_and_pickup"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="row align-items-center justify-content-between row-infos">
                <div className="col-lg-12 padding-r-0 padding-l-0">
                  <div className="margin-top-10 relative form-group pickup-address">
                    <label className="t-body-regular c-fake-black ">
                      {t('Delivery_address_LMP')}{' '}
                      <span className="c-red star-required">*</span>
                    </label>

                    <GeoLocation
                      t={t}
                      formik={formik}
                      names={{
                        country: 'delivery_address.country',
                        city: 'delivery_address.city',
                        state: 'delivery_address.state',
                        address: 'delivery_address.address',
                        zip_code: 'delivery_address.zip_code'
                      }}
                      disabled={formik?.values.project}
                      initialValues={{
                        country: formik?.values.delivery_address.country,
                        city: formik?.values.delivery_address.city,
                        state: formik?.values.delivery_address.state,
                        address: formik?.values.delivery_address.address,
                        zip_code: formik?.values.delivery_address.zip_code
                      }}
                    />
                  </div>
                </div>
              </div>
            </RenderIf>
            <div className="row align-items-center justify-content-between row-infos form-group">
              <div className="col-lg-12 padding-r-0 padding-l-0 ">
                <div className="margin-top-20 relative">
                  <label className="t-body-regular c-fake-black">
                    {isUS
                      ? t('Assurance_renonciation_dommage_question_us')
                      : t('Assurance_renonciation_dommage_question')}
                  </label>
                  <div className="d-flex">
                    <CheckboxInput
                      label={t('Need_one')}
                      name="assurance"
                      style="mr-2"
                      id="need-one"
                      defaultChecked
                      checked={!formik?.values.need_one}
                      onChange={() => handleShow(formik)}
                    />
                    <CheckboxInput
                      label={t('Have_one')}
                      name="assurance"
                      style="mr-2"
                      checked={formik?.values.need_one}
                      id="have-one"
                      onChange={() => handleShow(formik)}
                    />
                  </div>
                </div>
              </div>
            </div>

            <RenderIf condition={formik?.values.need_one}>
              <div className="row align-items-center justify-content-between row-infos form-group">
                <div className="col-lg-12 padding-r-0 padding-l-0 ">
                  <div className="margin-top-10 relative">
                    <Insurance
                      data={insurance.data}
                      address={insurance.address}
                      formik={formik}
                      t={t}
                      isFormik
                    />
                  </div>
                </div>
              </div>
            </RenderIf>

            <div className="row align-items-center justify-content-between row-infos form-group">
              <div className="col-lg-12 padding-r-0 padding-l-0 ">
                <div className="margin-top-10 relative">
                  <Input
                    placeholder={t('PO_number')}
                    label={t('PO_number')}
                    isNotRequired
                    formikTouched={formik?.touched}
                    formikErrors={formik?.errors}
                    name="po_number"
                  />
                </div>
              </div>
            </div>

            <div className="row-infos form-group pd-date-picker margin-top-10">
              <div className="col-lg-12 padding-r-0 padding-l-0 ">
                <div className="margin-top-10 relative">
                  <p className="t-body-regular c-fake-black">
                    <span className="t-body-regular c-fake-black">
                      {t('Rental_date')}
                    </span>
                  </p>
                  <DatePicker
                    startDateClassName="form-control w-100"
                    endDateClassName="form-control w-100"
                    handleStartDateChange={(date) =>
                      handleStartDateChange(date, formik)
                    }
                    handleEndDateChange={(date) =>
                      handleEndDateChange(date, formik)
                    }
                    filterDate={isDateDisabled}
                    startDate={formik?.values.start_date}
                    endDate={formik?.values.end_date}
                  />
                  <ErrorMessage
                    name="datePicker_validator"
                    component="span"
                    className="error-message"
                  />
                </div>
              </div>
            </div>
            <div className="row-infos form-group pd-date-picker margin-top-10">
              <div className="col-lg-12 padding-r-0 padding-l-0 ">
                <div className="margin-top-10 relative">
                  <p className="t-body-regular c-fake-black">
                    <span className="t-body-regular c-fake-black">
                      {t('Drop_pickup_hour')}
                    </span>
                    <span className="c-red star-required">*</span>
                  </p>
                  <div className="adjustPicker new-datepicker">
                    <div className="hour_picker m-1">
                      <DatePickerComponent
                        className="form-control w-100"
                        showTimeSelectOnly
                        timeIntervals={15}
                        selected={formik.values?.pickup_time?.pickup}
                        onChange={(date) => {
                          formik.setFieldValue('pickup_time.pickup', date);
                          const isValid = isDropTimeValid(
                            date,
                            formik.values.start_date,
                            formik.values.end_date,
                            formik.values.pickup_time.drop
                          );
                          formik.setFieldValue(
                            'pickup_time.pickup_time_validator',
                            isValid
                          );
                        }}
                        dateFormat="h:mm aa"
                        name="pickup_time.pickup"
                        minTime={pickupHours?.minTime}
                        maxTime={pickupHours?.maxTime}
                        placeholderText={t('Pickup_hour')}
                        showTimeSelect
                      />
                      <ErrorMessage
                        name="pickup_time.pickup"
                        component="span"
                        className="error-message"
                      />
                    </div>
                    <div className="hour_picker m-1">
                      <DatePickerComponent
                        showTimeSelectOnly
                        showTimeSelect
                        selected={formik.values?.pickup_time?.drop}
                        onChange={(date) => {
                          formik.setFieldValue('pickup_time.drop', date);
                          const isValid = isDropTimeValid(
                            formik.values.pickup_time.pickup,
                            formik.values.start_date,
                            formik.values.end_date,
                            date
                          );
                          formik.setFieldValue(
                            'pickup_time.pickup_time_validator',
                            isValid
                          );
                        }}
                        timeIntervals={15}
                        minTime={dropHours.minTime}
                        maxTime={dropHours.maxTime}
                        dateFormat="h:mm aa"
                        name="pickup_time.drop"
                        placeholderText={t('Drop_hour')}
                        className="form-control w-100"
                      />
                      <ErrorMessage
                        name="pickup_time.drop"
                        component="span"
                        className="error-message"
                      />
                      <ErrorMessage
                        name="pickup_time.pickup_time_validator"
                        component="span"
                        className="error-message"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="row-infos form-group pd-date-picker margin-top-10">
              <div className="col-lg-12 padding-r-0 padding-l-0 ">
                <div className="margin-top-10 relative">
                  <label className="label-input d-lg-block">
                    {t('Payment_method')}
                    <span className="c-red star-required">*</span>
                  </label>
                  <MultiSelect
                    isGeoLocation
                    options={[
                      { value: 'credit card', label: t('Credit_card') },
                      { value: 'credit account', label: t('Credit_account') }
                    ]}
                    t={t}
                    name="payment_method"
                    handleChange={(value) => {
                      onChange(value, 'payment_method', formik);
                      if (value?.value === 'credit account') {
                        formik?.setFieldValue('isCreditAccount', true);
                      } else {
                        formik?.setFieldValue('isCreditAccount', false);
                      }
                    }}
                    value={formik?.values.payment_method}
                    placeholder={t('Select')}
                  />
                  <ErrorMessage
                    name="payment_method"
                    component="span"
                    className="error-message"
                  />
                </div>
              </div>
            </div>
            <RenderIf condition={isMobile}>
              <div className="d-flex label-check align-items-center marg-top-20 d-lg-none">
                <input
                  name="accept_terms"
                  type="checkbox"
                  id="accept_terms"
                  onChange={() =>
                    formik?.setFieldValue(
                      'accept_terms',
                      !formik?.values.accept_terms
                    )
                  }
                  className="checkbox"
                />
                <label
                  className="c-fake-black ft-12"
                  htmlFor="accept_terms"
                ></label>
                <p className="ft-12 c-fake-black">
                  {t('Accept')}
                  <span
                    onClick={handleShowInfo}
                    className="c-red ml-1 bold underline-red
                  ft-12
                  "
                  >
                    {t('Booking_conditions')}{' '}
                  </span>
                </p>
              </div>
              <ErrorMessage
                name="accept_terms"
                component="span"
                className="error-message"
              />
            </RenderIf>
          </div>
        </div>

        <div className="text-center mt-4 fixed-button-modal">
          <CustomButton
            className="round-button black transparent bold mb-3"
            onClick={handleClose}
            textButton={t('Cancel')}
          />
          <CustomButton
            className="round-button yellow c-black  bold"
            type="submit"
            isLoading={isLoading}
            textPopper={
              isDisabled
                ? `${t('Minimum_rental_period_msg')} ${
                    formik.values.minimum_rental_period
                  } ${t('Days_with_space')}`
                : textButton
            }
            disabled={isLoading || isDisabled}
            textButton={textButton}
          />
        </div>
      </div>
    );
  };

  return (
    <RenderIf condition={show}>
      <EquipmentsModal
        data={{
          ...requestData,
          discount: promotion?.percent_off / 100
        }}
        onClose={handleClose}
        title={title}
        detectLanguage={detectLanguage}
        isBidz={isBidz}
        isUS={isUS}
        isBooking={isBooking}
        t={t}
      >
        <FormikForm
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={(values, actions) => {
            onSubmitFn(values, actions);
          }}
          component={RequestForm}
        />
        <RenderIf condition={showInfo}>
          <Modal
            t={t}
            onClose={handleShowInfo}
            className="t-header-h5 c-grey-titles"
            description="Booking_conditions"
            noScrollModal
          >
            <CustomLabel
              severity="info"
              text={t('Insurance_booking_req_text_1')}
              className="brd-radius-20 project-info-lbl"
            />
            <CustomLabel
              severity="info"
              text={t('Insurance_booking_req_text_2')}
              className="brd-radius-20 project-info-lbl"
            />
            <CustomLabel
              severity="info"
              text={t('Insurance_booking_req_text_3')}
              className="brd-radius-20 project-info-lbl"
            />
          </Modal>
        </RenderIf>
      </EquipmentsModal>
    </RenderIf>
  );
}

RequestConfirmationModal.propTypes = {
  data: PropTypes.object,
  t: PropTypes.func,
  show: PropTypes.bool,
  handleClose: PropTypes.func,
  detectLanguage: PropTypes.string.isRequired,
  onSubmitFn: PropTypes.func.isRequired,
  isBooking: PropTypes.bool,
  isBidz: PropTypes.bool,
  textButton: PropTypes.string
};

RequestConfirmationModal.defaultProps = {
  show: false,
  isBooking: false,
  isBidz: false,
  textButton: ''
};
