import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import 'swiper/css/autoplay';
import { Pagination, Navigation, Autoplay } from 'swiper/modules';

export default function PartnerCarousel({ carouselItems, isMobile, isDesktop }) {
  // Determine slides per view based on screen size
  const getSlidesPerView = () => {
    if (isMobile) return 1;
    if (isDesktop) return 5;
    return 3; // tablet
  };

  const getSpaceBetween = () => {
    if (isMobile) return 20;
    if (isDesktop) return 40;
    return 30; // tablet
  };

  return (
    <div className="partner-carousel-container">
      <Swiper
        slidesPerView={getSlidesPerView()}
        spaceBetween={getSpaceBetween()}
        centeredSlides={false}
        loop={true}
        autoplay={{
          delay: 3000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}
        pagination={{
          el: '.partner-pagination',
          clickable: true,
          dynamicBullets: true,
        }}
        navigation={{
          nextEl: '.partner-button-next',
          prevEl: '.partner-button-prev',
        }}
        modules={[Pagination, Navigation, Autoplay]}
        className="partner-swiper"
        breakpoints={{
          320: {
            slidesPerView: 1,
            spaceBetween: 20,
          },
          768: {
            slidesPerView: 3,
            spaceBetween: 30,
          },
          1024: {
            slidesPerView: 4,
            spaceBetween: 35,
          },
          1200: {
            slidesPerView: 5,
            spaceBetween: 40,
          },
        }}
      >
        {carouselItems.map((item, index) => (
          <SwiperSlide key={index}>
            <div className="partner-slide">
              <a 
                href={item.link} 
                target="_blank" 
                rel="noreferrer"
                className="partner-link"
              >
                <div className="partner-logo-container">
                  <img 
                    src={item.src} 
                    alt={item.alt || "Partner logo"} 
                    className="partner-logo"
                  />
                </div>
              </a>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>

      <div className="partner-controls">
        <div className="partner-navigation">
          <div className="partner-button-prev partner-nav-button">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          <div className="partner-pagination"></div>
          <div className="partner-button-next partner-nav-button">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
}
