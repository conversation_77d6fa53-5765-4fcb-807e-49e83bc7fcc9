import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';
import 'swiper/css/zoom';
import 'swiper/css/navigation';
import { EffectCoverflow, Pagination, Navigation } from 'swiper/modules';

import 'swiper/swiper-bundle.css';

export default function Slider3D({
  carouselItems,
  isMobile,
  showFiveSlides = false
}) {
  // Determine slides per view based on screen size and showFiveSlides prop
  const getSlidesPerView = () => {
    if (isMobile) return 1;
    if (showFiveSlides) return 5;
    return 2;
  };

  return (
    <Swiper
      effect={'coverflow'}
      grabCursor={true}
      centeredSlides={true}
      loop={true}
      slidesPerView={getSlidesPerView()}
      coverflowEffect={{
        rotate: 0,
        stretch: 0,
        depth: 100,
        modifier: showFiveSlides ? 1 : 3
      }}
      pagination={{ el: '.swiper-pagination', clickable: true }}
      navigation={{
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev',
        clickable: true
      }}
      modules={[EffectCoverflow, Pagination, Navigation]}
      className={`swiper_container ${
        showFiveSlides ? 'swiper-five-slides' : ''
      }`}
    >
      {carouselItems.map((item, index) => {
        return (
          <SwiperSlide key={index} zoom>
            <div className="swiper-slidee">
              <a href={item.link} target="_blank" rel="noreferrer">
                <img src={item.src} alt="" />
              </a>
            </div>
          </SwiperSlide>
        );
      })}

      <div className="slider-controler">
        <div className="text-center relative d-flex align-items-center justify-content-center">
          <div className="swiper-button-prev slider-arrow">
            <ion-icon name="arrow-back-outline"></ion-icon>
          </div>
          <div className="swiper-button-next slider-arrow">
            <ion-icon name="arrow-forward-outline"></ion-icon>
          </div>
          <div className="swiper-pagination"></div>
        </div>
      </div>
    </Swiper>
  );
}
