.partner-carousel-container {
  position: relative;
  padding: 40px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 24px;
  margin: 20px 0;

  .partner-swiper {
    padding: 20px 0 60px;
    overflow: visible;

    .swiper-wrapper {
      align-items: center;
    }
  }

  .partner-slide {
    height: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    opacity: 0;
    animation: slideInUp 0.6s ease forwards;

    &:hover {
      transform: translateY(-5px);
    }

    @for $i from 1 through 10 {
      &:nth-child(#{$i}) {
        animation-delay: #{$i * 0.1}s;
      }
    }
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .partner-link {
    display: block;
    width: 100%;
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  .partner-logo-container {
    background: #fff;
    border-radius: 20px;
    padding: 30px 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    min-height: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
      );
      transition: left 0.5s ease;
    }

    &:hover {
      box-shadow: 0 12px 48px rgba(0, 0, 0, 0.12);
      border-color: rgba(236, 168, 105, 0.3);

      &::before {
        left: 100%;
      }
    }

    @media (max-width: 768px) {
      padding: 25px 15px;
      min-height: 120px;
    }
  }

  .partner-logo {
    max-width: 100%;
    max-height: 80px;
    width: auto;
    height: auto;
    object-fit: contain;
    filter: grayscale(20%);
    transition: all 0.3s ease;

    .partner-link:hover & {
      filter: grayscale(0%);
    }

    @media (max-width: 768px) {
      max-height: 60px;
    }
  }

  .partner-controls {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .partner-navigation {
    display: flex;
    align-items: center;
    gap: 30px;

    @media (max-width: 768px) {
      gap: 20px;
    }
  }

  .partner-nav-button {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: #fff;
    border: 2px solid #e5ecf6;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #666;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);

    &:hover {
      background: #f8f9fa;
      border-color: #eca869;
      color: #eca869;
      transform: scale(1.1);
      box-shadow: 0 6px 24px rgba(236, 168, 105, 0.2);
    }

    &.swiper-button-disabled {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        transform: none;
        background: #fff;
        border-color: #e5ecf6;
        color: #666;
      }
    }

    @media (max-width: 768px) {
      width: 40px;
      height: 40px;
    }

    svg {
      width: 20px;
      height: 20px;

      @media (max-width: 768px) {
        width: 16px;
        height: 16px;
      }
    }
  }

  .partner-pagination {
    display: flex;
    align-items: center;
    gap: 8px;

    .swiper-pagination-bullet {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #d1d5db;
      opacity: 1;
      transition: all 0.3s ease;
      cursor: pointer;

      &.swiper-pagination-bullet-active {
        background: #eca869;
        transform: scale(1.2);
      }

      &:hover {
        background: #eca869;
        transform: scale(1.1);
      }
    }

    &.swiper-pagination-bullets-dynamic {
      .swiper-pagination-bullet {
        transition: all 0.3s ease;
      }
    }
  }

  // Responsive adjustments
  @media (max-width: 1200px) {
    .partner-logo-container {
      padding: 25px 15px;
      min-height: 130px;
    }

    .partner-logo {
      max-height: 70px;
    }
  }

  @media (max-width: 992px) {
    padding: 30px 0;

    .partner-logo-container {
      padding: 20px 15px;
      min-height: 120px;
    }

    .partner-logo {
      max-height: 65px;
    }
  }

  @media (max-width: 768px) {
    padding: 20px 0;

    .partner-swiper {
      padding: 15px 0 50px;
    }

    .partner-navigation {
      gap: 15px;
    }
  }
}
