// Partner Carousel Styles - Optimized for horizontal logos
.partner-carousel {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at 30% 20%,
        rgba(236, 168, 105, 0.1) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 70% 80%,
        rgba(212, 149, 107, 0.1) 0%,
        transparent 50%
      );
    pointer-events: none;
  }

  .partner-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 1;
  }

  .partner-header {
    text-align: center;
    margin-bottom: 60px;

    h2 {
      font-size: 3rem;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 20px;
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-shadow: 0 4px 8px rgba(44, 62, 80, 0.1);

      @media (max-width: 768px) {
        font-size: 2.2rem;
      }
    }

    p {
      font-size: 1.2rem;
      color: #6c757d;
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;

      @media (max-width: 768px) {
        font-size: 1rem;
        padding: 0 10px;
      }
    }
  }

  .partner-swiper {
    padding: 40px 0 80px;
    position: relative;

    .swiper-wrapper {
      align-items: center;
      // Ensure all slides are at the same baseline
      display: flex;
      align-items: stretch;
    }
  }

  .partner-slide {
    height: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    // Ensure consistent height across all slides
    min-height: 100%;
  }

  .partner-link {
    display: flex;
    width: 100%;
    height: 100%;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    // Ensure perfect centering within the link
    align-items: center;
    justify-content: center;

    &:hover {
      transform: translateY(-8px);
    }
  }

  .partner-logo-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid transparent;
    background-clip: padding-box;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), 0 4px 8px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    // Ensure perfect centering and alignment for all logo sizes
    text-align: center;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
      );
      transition: left 0.6s ease;
    }

    &:hover {
      transform: translateY(-4px) scale(1.02);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
      border-color: rgba(236, 168, 105, 0.3);

      &::before {
        left: 100%;
      }
    }

    // Desktop sizes - optimized for horizontal logos
    @media (min-width: 1200px) {
      padding: 50px 60px;
      height: 220px;
      border-radius: 28px;
    }

    @media (min-width: 992px) and (max-width: 1199px) {
      padding: 40px 50px;
      height: 200px;
      border-radius: 26px;
    }

    @media (min-width: 768px) and (max-width: 991px) {
      padding: 35px 40px;
      height: 160px;
      border-radius: 22px;
    }

    @media (max-width: 767px) {
      padding: 25px 30px;
      height: 120px;
      border-radius: 20px;
    }
  }

  .partner-logo {
    max-width: 85%; // Allow horizontal logos to use most of the width
    width: auto;
    height: auto;
    object-fit: contain;
    filter: grayscale(15%) brightness(0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    // Perfect centering and alignment - let flexbox handle it
    display: block;
    margin: 0 auto;
    // Ensure consistent baseline alignment
    vertical-align: middle;

    .partner-link:hover & {
      filter: grayscale(0%) brightness(1);
      transform: scale(1.08);
    }

    // Desktop - optimized for horizontal logos
    @media (min-width: 1200px) {
      max-height: 100px;
      max-width: 280px; // Generous width for horizontal logos
    }

    @media (min-width: 992px) and (max-width: 1199px) {
      max-height: 85px;
      max-width: 240px;
    }

    @media (min-width: 768px) and (max-width: 991px) {
      max-height: 70px;
      max-width: 200px;
    }

    @media (max-width: 767px) {
      max-height: 50px;
      max-width: 160px;
    }
  }

  // Navigation Styles
  .partner-navigation {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 40px;

    .partner-button-prev,
    .partner-button-next {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #eca869 0%, #d4956b 100%);
      border: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4px 15px rgba(236, 168, 105, 0.3);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        transition: all 0.3s ease;
        transform: translate(-50%, -50%);
      }

      &:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 25px rgba(236, 168, 105, 0.4);

        &::before {
          width: 100%;
          height: 100%;
        }
      }

      &:active {
        transform: translateY(0) scale(0.98);
      }

      &.swiper-button-disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
        box-shadow: 0 2px 8px rgba(236, 168, 105, 0.2);
      }

      svg {
        width: 24px;
        height: 24px;
        fill: white;
        transition: transform 0.3s ease;
      }

      &:hover svg {
        transform: scale(1.1);
      }
    }
  }

  // Pagination Styles
  .partner-pagination {
    display: flex;
    justify-content: center;
    margin-top: 30px;

    .swiper-pagination-bullet {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #d1d5db;
      opacity: 1;
      margin: 0 6px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: linear-gradient(135deg, #eca869 0%, #d4956b 100%);
        border-radius: 50%;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translate(-50%, -50%);
      }

      &.swiper-pagination-bullet-active {
        background: linear-gradient(135deg, #eca869 0%, #d4956b 100%);
        transform: scale(1.3);

        &::before {
          width: 100%;
          height: 100%;
        }
      }

      &:hover {
        background: linear-gradient(135deg, #eca869 0%, #d4956b 100%);
        transform: scale(1.2);
        box-shadow: 0 2px 8px rgba(236, 168, 105, 0.3);
      }
    }

    &.swiper-pagination-bullets-dynamic {
      .swiper-pagination-bullet {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  }

  // Responsive adjustments
  @media (max-width: 992px) {
    padding: 30px 0;

    .partner-logo-container {
      padding: 20px 15px;
      min-height: 120px;
    }

    .partner-logo {
      max-height: 65px;
    }
  }

  @media (max-width: 768px) {
    padding: 20px 0;

    .partner-swiper {
      padding: 15px 0 50px;
    }

    .partner-navigation {
      gap: 15px;
    }
  }
}
