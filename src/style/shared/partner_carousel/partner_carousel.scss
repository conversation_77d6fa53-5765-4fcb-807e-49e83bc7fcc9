.partner-carousel-container {
  position: relative;
  padding: 60px 40px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f8f9fa 100%);
  border-radius: 32px;
  margin: 40px 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(236, 168, 105, 0.08);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(236, 168, 105, 0.02) 0%, transparent 50%, rgba(236, 168, 105, 0.02) 100%);
    border-radius: 32px;
    pointer-events: none;
  }

  .partner-swiper {
    padding: 30px 0 80px;
    overflow: visible;
    position: relative;
    z-index: 1;

    .swiper-wrapper {
      align-items: center;
    }
  }

  @media (max-width: 992px) {
    padding: 40px 20px;
    margin: 30px 0;
    border-radius: 24px;

    &::before {
      border-radius: 24px;
    }

    .partner-swiper {
      padding: 20px 0 60px;
    }
  }

  @media (max-width: 768px) {
    padding: 30px 15px;
    margin: 20px 0;
    border-radius: 20px;

    &::before {
      border-radius: 20px;
    }

    .partner-swiper {
      padding: 15px 0 50px;
    }
  }
}

  .partner-slide {
    height: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    opacity: 0;
    animation: slideInUp 0.6s ease forwards;

    &:hover {
      transform: translateY(-5px);
    }

    @for $i from 1 through 10 {
      &:nth-child(#{$i}) {
        animation-delay: #{$i * 0.1}s;
      }
    }
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .partner-link {
    display: block;
    width: 100%;
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  .partner-logo-container {
    background: #fff;
    border-radius: 24px;
    padding: 50px 30px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(236, 168, 105, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
      );
      transition: left 0.5s ease;
    }

    &:hover {
      box-shadow: 0 20px 64px rgba(236, 168, 105, 0.25);
      border-color: rgba(236, 168, 105, 0.4);
      transform: translateY(-8px) scale(1.02);

      &::before {
        left: 100%;
      }
    }

    // Desktop sizes - much bigger cards
    @media (min-width: 1200px) {
      padding: 60px 40px;
      min-height: 240px;
      border-radius: 28px;
    }

    @media (min-width: 992px) and (max-width: 1199px) {
      padding: 50px 35px;
      min-height: 220px;
      border-radius: 26px;
    }

    @media (min-width: 768px) and (max-width: 991px) {
      padding: 40px 25px;
      min-height: 180px;
      border-radius: 22px;
    }

    @media (max-width: 767px) {
      padding: 30px 20px;
      min-height: 140px;
      border-radius: 20px;
    }
  }

  .partner-logo {
    max-width: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    filter: grayscale(15%) brightness(0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    .partner-link:hover & {
      filter: grayscale(0%) brightness(1);
      transform: scale(1.05);
    }

    // Desktop - much larger logos
    @media (min-width: 1200px) {
      max-height: 120px;
    }

    @media (min-width: 992px) and (max-width: 1199px) {
      max-height: 100px;
    }

    @media (min-width: 768px) and (max-width: 991px) {
      max-height: 80px;
    }

    @media (max-width: 767px) {
      max-height: 60px;
    }
  }

  .partner-controls {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .partner-navigation {
    display: flex;
    align-items: center;
    gap: 30px;

    @media (max-width: 768px) {
      gap: 20px;
    }
  }

  .partner-nav-button {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid rgba(236, 168, 105, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    color: #666;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(236, 168, 105, 0.1) 0%, transparent 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      background: linear-gradient(135deg, #eca869 0%, #d4956b 100%);
      border-color: #eca869;
      color: #fff;
      transform: scale(1.15) translateY(-2px);
      box-shadow: 0 12px 32px rgba(236, 168, 105, 0.3);

      &::before {
        opacity: 1;
      }
    }

    &.swiper-button-disabled {
      opacity: 0.4;
      cursor: not-allowed;

      &:hover {
        transform: none;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-color: rgba(236, 168, 105, 0.2);
        color: #666;
        box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
      }
    }

    @media (max-width: 992px) {
      width: 48px;
      height: 48px;
    }

    @media (max-width: 768px) {
      width: 44px;
      height: 44px;
    }

    svg {
      width: 24px;
      height: 24px;
      position: relative;
      z-index: 1;

      @media (max-width: 992px) {
        width: 20px;
        height: 20px;
      }

      @media (max-width: 768px) {
        width: 18px;
        height: 18px;
      }
    }
  }

  .partner-pagination {
    display: flex;
    align-items: center;
    gap: 12px;

    .swiper-pagination-bullet {
      width: 14px;
      height: 14px;
      border-radius: 50%;
      background: rgba(209, 213, 219, 0.6);
      opacity: 1;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      border: 2px solid transparent;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: -4px;
        left: -4px;
        right: -4px;
        bottom: -4px;
        border: 2px solid transparent;
        border-radius: 50%;
        transition: all 0.3s ease;
      }

      &.swiper-pagination-bullet-active {
        background: linear-gradient(135deg, #eca869 0%, #d4956b 100%);
        transform: scale(1.3);
        box-shadow: 0 4px 12px rgba(236, 168, 105, 0.4);

        &::before {
          border-color: rgba(236, 168, 105, 0.3);
        }
      }

      &:hover {
        background: linear-gradient(135deg, #eca869 0%, #d4956b 100%);
        transform: scale(1.2);
        box-shadow: 0 2px 8px rgba(236, 168, 105, 0.3);
      }
    }

    &.swiper-pagination-bullets-dynamic {
      .swiper-pagination-bullet {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  }

  // Responsive adjustments
  @media (max-width: 1200px) {
    .partner-logo-container {
      padding: 25px 15px;
      min-height: 130px;
    }

    .partner-logo {
      max-height: 70px;
    }
  }

  @media (max-width: 992px) {
    padding: 30px 0;

    .partner-logo-container {
      padding: 20px 15px;
      min-height: 120px;
    }

    .partner-logo {
      max-height: 65px;
    }
  }

  @media (max-width: 768px) {
    padding: 20px 0;

    .partner-swiper {
      padding: 15px 0 50px;
    }

    .partner-navigation {
      gap: 15px;
    }
  }
}
