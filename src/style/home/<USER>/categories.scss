.container-card {
  justify-content: center;
  @media (max-width: 992px) {
    flex-wrap: nowrap;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    overflow: hidden;
    overflow-x: scroll;
    -webkit-overflow-scrolling: touch;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar {
    width: 1px;
    height: 1px;
  }

  .card-categorie {
    width: 244px;
    cursor: pointer;
    padding: 0;
    margin-right: 16px;

    .similarProduct-box {
      padding: 19px 13px 19px 13px;
      background: $white;
      border: 1px solid $border-grey;
      border-radius: 7px;
      box-shadow: 0px 11px 40px 0px #061c3d12;
      transition: all ease-in-out 0.5s;

      &:hover {
        border-color: $yellow;
        box-shadow: 0 11px 50px 0 rgba(6, 28, 61, 0.2);
      }

      img {
        height: 160px;
      }

      h3 {
        @media (min-width: 992px) {
          margin-top: 20px;
          min-height: 52px;
        }
      }

      .round-button.yellow {
        background: none;
        border: none;
        font-size: 14px;
        color: $neutrals-gray;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        margin: 0 auto;

        &:after {
          content: url('../style/assets/img/Icons/ArrowRightGray.svg');
          height: 20px;
          padding-left: 8px;
        }
      }
    }
  }
}

.title-categories {
  margin-bottom: 4%;
  flex-direction: column-reverse;
  @media (min-width: 992px) {
    flex-direction: initial;
  }

  .text-align-right {
    text-align: right;
    @media (min-width: 992px) {
      text-align: initial;
    }
  }

  @media (max-width: 992px) {
    .arrow-mobile {
      position: absolute;
      right: 0;
      width: auto;
      transform: rotate(-90deg);
    }
  }
}

.title-category {
  margin-left: 0;
  margin-top: 5%;
  color: $white;
  font-size: 25px;
  line-height: normal;
  @media (max-width: 992px) {
    font-size: 20px;
  }

  @media (min-width: 1400px) {
    font-size: 50px;
  }
}

.cloud-img {
  @media (max-width: 760px) {
    width: 230px;
  }
}

.App {
  display: contents;
}

.container-categories {
  padding: 30px 0 20px;
  @media (min-width: 572px) {
    margin-top: 50px;
  }
  @media (min-width: 992px) {
    margin-top: 0;
    margin-bottom: 20px;
    padding: 10px 0;
  }

  @media (max-width: 992px) {
    padding: 45px 0 20px;
  }

  .horizontal-scroll-wrapper {
    flex-wrap: nowrap;
    @media (max-width: 992px) {
      margin-right: -22px;
    }

    .card_item {
      width: max-content;
      text-align: center;
      padding: 0;
      margin: 0 11px;

      img {
        width: 35px;
        height: 35px;
      }

      a {
        text-decoration: none;
      }

      .card {
        background: none;
        border: none;

        &.is-selected {
          h3 {
            &:after {
              content: '';
              width: 100%;
              height: 2px;
              background: $fake-black;
              display: block;
            }
          }
        }

        h3 {
          margin-bottom: 0;
        }

        .content-details {
          width: 24px;
          height: 24px;
          margin: 0 auto 10px;
          background: $white;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    &.rental_centers {
      .content_card {
        width: 170px;
        height: 75px;
        border-radius: 7px;
        border: 1px solid $border-grey;
        opacity: 0.5;
        background: $white;
        box-shadow: 0 11px 40px 0 rgba(6, 28, 61, 0.07);
      }

      .card_item {
        max-width: max-content;
        margin: 0 12px;

        .card {
          &.is-selected {
            border-color: $yellow;

            h3 {
              &:after {
                display: none;
              }
            }
          }
        }
      }
    }
  }
}

.title_home {
  max-width: 545px;
  margin: 0 auto 45px;
  text-align: center;

  p {
    margin: 0 auto;
    max-width: 400px;
  }
}

.overflow_categories_row {
  margin-top: 35px;
  @media (max-width: 992px) {
    overflow-x: auto;
    margin-bottom: 35px;
    margin-top: 0;
    display: flex;
    justify-content: start !important;
  }
}

.categories_row {
  @media (max-width: 992px) {
    display: flex;
    flex-wrap: nowrap;
    margin-bottom: 0;
  }
}

.lodger_provider {
  .row {
    @media (max-width: 992px) {
      display: flex;
      flex-wrap: nowrap;
    }
  }
}

.equipement_rentals {
  margin: 50px 0 17px;
  @media (max-width: 992px) {
    margin: 38px 0 30px;
  }

  .round-button {
    @media (max-width: 380px) {
      font-size: 13px;
    }
  }
}

.horizontal-scroll-wrapper {
  .swiper-explore {
    .swiper-home {
      @media (max-width: 992px) {
        max-width: 64%;
      }
    }
  }
  .swiper-home {
    max-width: 95%;
    margin: 0 auto 24px;
    justify-content: start;
    @media (min-width: 992px) {
      justify-content: center;
    }

    .scrollmenu-explore {
      display: flex;

      span {
        display: inline-flex;
        width: 170px;
        height: 75px;
        border-radius: 7px;
        border: 1.5px solid #e5ecf6;
        background: $white;
        box-shadow: 0px 4px 17px 0px #adadad12;
        padding: 14.97px;
        gap: 14.97px;
        min-width: 170px;
        margin: 0 12px;
        justify-content: center;
        align-items: center;

        &.selected {
          border-color: $yellow;
        }

        img {
          max-height: 70px;
        }
      }
    }

    .chevron-left {
      margin-right: 12px;
    }

    .chevron-right {
      margin-left: 12px;
    }
  }
}

.swiper_after {
  position: relative;

  @media (min-width: 992px) {
    &:after {
      content: url('../style/assets/img/Icons/Layer_home.svg');
      display: block;
      position: absolute;
      bottom: -280px;
    }
  }
}

.swiper-3d {
  .swiper-slide {
    height: auto;
    border-radius: 36px;
    background: #fff;
    box-shadow: 0 3.633px 44.505px 0 rgba(24, 29, 69, 0.09);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    @media (min-width: 992px) {
      width: 581px !important;
      height: 290px !important;
    }

    .swiper-slide-shadow-left,
    .swiper-slide-shadow-right {
      display: none;
    }

    img {
      max-width: 80%;
      display: block;
      margin: 0 auto;
      @media (min-width: 992px) {
        max-width: 380px;
      }
    }
  }

  // Special styles for 5-slide view on desktop
  &.swiper-five-slides {
    @media (min-width: 1024px) {
      .swiper-slide {
        width: auto !important;
        max-width: 280px !important;
        height: 200px !important;
        margin: 0 10px;
      }

      .swiper-wrapper {
        justify-content: center;
      }
    }
  }

  .slider-controler {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 50px;

    .swiper-pagination-horizontal.swiper-pagination-bullets {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .swiper-pagination-bullet {
        background: $yellow;
        width: 11px;
        height: 11px;

        &.swiper-pagination-bullet-active {
          width: 15px;
          height: 15px;
        }
      }
    }
  }

  .swiper-button-next,
  .swiper-button-prev {
    position: absolute;
    right: auto;
    left: auto;
    top: auto;
    height: 23px;
    width: 22px;

    &:after {
      font-family: inherit;
      content: url('../style/assets/img/Icons/ArrowRightSlider.svg');
      height: 23px;
      width: 22px;
      display: contents;
    }
  }

  .swiper-button-prev {
    transform: rotate(180deg);
    left: -30px;
    top: 13px;
  }

  .swiper-button-next {
    right: -30px;
  }
}

.bordred_title {
  margin-bottom: 60px;

  // mobile
  @media (max-width: 992px) {
    margin-bottom: 35px;
  }

  h2 {
    position: relative;
    display: inline-block;

    &:after {
      content: '';
      display: inline-block;
      width: 100%;
      height: 3px;
      position: absolute;
      left: 0;
      right: 0;
      bottom: -12px;
      border-radius: var(--none, 0px);
      background: linear-gradient(
        270deg,
        rgba(236, 168, 105, 0) 0%,
        rgba(236, 168, 105, 0.5) 53.65%,
        rgba(236, 168, 105, 0) 100%
      );
    }
  }
}

.w-90 {
  width: 90%;
}

.zoom {
  transition: all 0.5s ease-in-out;
  &:hover {
    transform: scale(1.1);
    background-color: white !important;
  }
}
