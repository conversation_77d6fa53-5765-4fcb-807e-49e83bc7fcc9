import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Configure, Menu } from 'react-instantsearch-dom';
import { useEquipper } from '../../shared/context/Equipper_context';
import Categories from '../../components/home/<USER>/Categories';
import Search from '../../components/home/<USER>/Search';
import SearchProvider from '../../shared/context/Search_context';
import LodgerProvider from '../../shared/context/Lodger_context';
import { CustomInventoryInfiniteHits } from '../../components/search_result/Custom_inventory_infinite_hits';
import InstantSearchAlgolia from '../../components/search_result/Instant_search_algolia';
import RenderIf from '../../shared/components/Render_if';
import Construction_bro from '../../style/assets/img/Construction-bro.png';
import { getCookies } from '../../shared/helpers/Cookies';
import CustomImage from '../../shared/components/images/Custom_image';
import { carouselItems } from '../../shared/helpers/Data_helper';
import Swipe from '../../shared/components/swipe/Swipe';
import PartnerCarousel from '../../shared/components/partner_carousel/Partner_carousel';
import useResponsive from '../../shared/helpers/Responsive';
import BBL from '../../style/assets/img/contractors/BBL_Logo.png';
import Solico from '../../style/assets/img/contractors/solico-construction-logo.png';
import Wilmar from '../../style/assets/img/contractors/Wilmar.png';
import Germano from '../../style/assets/img/contractors/Germano.png';
import Paul from '../../style/assets/img/contractors/PaulFelix.png';
import Charex from '../../style/assets/img/contractors/charex.png';
import Beaudoin from '../../style/assets/img/contractors/Beaudoin.png';
import {
  getSessionStorage,
  setSessionStorage
} from '../../shared/helpers/Session_storage_helper';

const contractors = [
  {
    key: 1,
    name: ' Germano Construction',
    src: Germano,
    link: ' https://www.germanoconstruction.com/en/'
  },
  {
    key: 2,
    name: 'Construction Wilmar',
    src: Wilmar,
    link: 'https://constructionwilmar.com/'
  },
  {
    key: 6,
    name: 'Beaucoin Canada',
    src: Beaudoin,
    link: 'https://beaudoincanada.com/'
  },
  {
    key: 7,
    name: 'Charex',
    src: Charex,
    link: 'https://charex.ca/'
  },
  {
    key: 3,
    name: 'BBL Construction',
    src: BBL,
    link: 'https://www.bblconstruction.ca/'
  },
  {
    key: 4,
    name: 'Solico',
    src: Solico,
    link: 'https://solico.ca/'
  },
  {
    key: 5,
    name: 'PAUL FELX ENTREPRISES LTÉE',
    src: Paul,
    link: 'https://www.portailconstructo.com/batiguide/paul_felx_entreprises_ltee'
  }
];

export default function Home({
  t,
  detectLanguage,
  setShowFPModal,
  showFPModal,
  signIn,
  country
}) {
  const navigate = useNavigate();
  const { GetEquippersByCountry } = useEquipper();
  const token = getCookies('token');
  const [auth, setAuth] = useState(false);
  const [selectedEquipper, setSelectedEquipper] = useState(null);
  const [equippersInfo, setEquippersInfo] = useState([]);
  const [searchState, setSearchState] = useState({
    query: '',
    status: 'available'
  });
  const { isMobile, isDesktop } = useResponsive();
  const handleStateSwitch = (searchState) => {
    setSearchState(searchState);
  };
  useEffect(() => {
    async function fetchEquippers() {
      try {
        const response = await GetEquippersByCountry(country);
        if (response && response.status === 200 && response.data) {
          const activeEquippers = response.data.filter(
            (equipper) => equipper.has_inventory === true
          );

          setEquippersInfo(activeEquippers);
          if (!selectedEquipper && activeEquippers.length > 0) {
            if (activeEquippers.length > 2) {
              const splitIndex = Math.floor(activeEquippers.length / 2);
              setSelectedEquipper(activeEquippers[splitIndex]);
            } else {
              setSelectedEquipper(activeEquippers[0]);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching equipper info:', error);
      }
    }

    fetchEquippers();

    if (selectedEquipper) {
      setSearchState({
        menu: {
          equipper_id: selectedEquipper.id,
          status: 'available'
        }
      });
    }
  }, [GetEquippersByCountry, selectedEquipper, country]);

  const handleSelectEquipper = (equipper) => {
    setSelectedEquipper(equipper);
    setSearchState({
      menu: {
        equipper_id: equipper.id,
        status: 'available'
      }
    });
  };

  const handleIsActive = (equipper) => {
    if (selectedEquipper) {
      return selectedEquipper.id === equipper.id;
    }
    return false;
  };

  const startDate = new Date();
  const date = new Date();
  const endDate = new Date(date.setDate(date.getDate() + 6));

  useEffect(() => {
    if (token) {
      setAuth(true);
    } else {
      setAuth(false);
    }
  }, [token]);

  return (
    <>
      <div className="container">
        {!auth && (
          <div className="equipement_rentals ">
            <div className="row flex-lg-row flex-column-reverse align-items-center justify-content-center">
              <div className="col-lg-7">
                <h1 className="t-header-h1 c-fake-black">
                  {t('Equipement_home')}
                </h1>
                <p className="t-subheading-2 c-neutrals-gray mt-4 mb-4">
                  {t('Equipement_text_home')}
                </p>
                <div className="d-flex align-items-center">
                  <button className="round-button yellow c-white hover_black me-3 explore-btn">
                    <a href="#contactUS" className="c-white">
                      {' '}
                      {t('Book_demo')}
                    </a>
                  </button>
                  <button className="round-button fake-black c-white explore-btn">
                    <a href="#ourAffiliates" className="c-white">
                      {' '}
                      {t('Explore_word')}
                    </a>
                  </button>
                </div>
              </div>
              <div className="col-lg-4 offset-lg-1 text-lg-start text-center">
                <img src={Construction_bro} alt="home construction" />
              </div>
            </div>
          </div>
        )}
      </div>
      <div id="categories">
        <Categories
          t={t}
          detectLanguage={detectLanguage}
          setShowFPModal={setShowFPModal}
          showFPModal={showFPModal}
          signIn={signIn}
        />
      </div>
      <SearchProvider>
        <Search t={t} detectLanguage={detectLanguage} />
      </SearchProvider>
      <RenderIf condition={equippersInfo.length > 0}>
        <div className="container-categories" id="ourAffiliates">
          <div className="container">
            <div className="container-card horizontal-scroll-wrapper squares mt-lg-5 rental_centers">
              <div className="title_home bordred_title">
                <h2 className="t-header-h2 c-fake-black mb-1">
                  {t('Explore_rentals')}
                </h2>
              </div>
              <div className="swiper-explore">
                <Swipe
                  showArrow={equippersInfo.length > 5 || isMobile}
                  id="scrollmenu-explore"
                  className="scrollmenu scrollmenu-explore"
                >
                  {equippersInfo.map((equipper) => (
                    <span
                      key={equipper.id}
                      className={`${
                        handleIsActive(equipper) ? 'selected' : ''
                      }`}
                      onClick={() => handleSelectEquipper(equipper)}
                    >
                      <CustomImage
                        imageUrl={equipper.photo_url}
                        alt="equipper"
                        isUser
                      />
                    </span>
                  ))}
                </Swipe>
              </div>

              <LodgerProvider>
                <InstantSearchAlgolia
                  searchState={searchState}
                  onSearchStateChange={handleStateSwitch}
                  indexName={import.meta.env.VITE_ALGOLIA_INDEX_NAME}
                >
                  <Configure
                    filters={
                      'name:"Overseeder" OR name:"Fiber Blower" OR name:"Lasher" OR name:"Testing Equipment" OR name:"Fusion Splicer" OR name:"Hydraulic Piling Rig" OR name:"Compact Piling Rig" OR name:"Long-Reach Piling Rig" OR name:"MULTI PURPOSE PILING RIG" OR name:"HYDRAULIC DRILLING RIG" OR name:"ROTARY PILING RIG" OR name:"Compact Jet Grout Unit" OR name:"MANUAL GROUT MIXING" OR name:"COMPACT JET UNIT" OR name:"Grout Mixer Plant" OR name:"Articulated Tandem Rollers" OR name:"Skid-steer" OR name:"Lawn roller" OR name:"Skid-steer loader" OR name:"backhoe" OR name:"Winch" OR name:"straight boom lift" OR name:"Crane Lift" OR name:"bulldozer" OR name:"loader" OR name:"forklift" OR name:"roller" OR name:"skid steer" OR name:"boom lift" OR name:"scissor lift" OR name:"telescopic handler" OR name:"compactor" OR name:"crane" OR name:"dump truck" OR name:"water truck" OR name:"wheel loader" OR name:"mini excavator" OR name:"mini loader" OR name:"mini compactor" OR name:"mini forklift" OR name:"mini roller" OR name:"mini scissor lift" OR name:"Excavator" OR name:"mini skid steer" OR name:"mini backhoe" OR name:"mini bulldozer" OR name:"mini crane" OR name:"mini dump truck" OR name:"mini water truck" OR name:"mini boom lift" OR name:"mini telescopic handler" OR name:"Vibratory roller" OR name:"Trench cutter"'
                    }
                  />
                  <Menu attribute="equipper_id" className="hidden" />
                  <CustomInventoryInfiniteHits
                    detectLanguage={detectLanguage}
                    t={t}
                    isEquipperExploreMore
                    setShowFPModal={setShowFPModal}
                    showFPModal={showFPModal}
                    signIn={signIn}
                    isExploreMore
                  />
                </InstantSearchAlgolia>
              </LodgerProvider>
              <div className="text-center request-all w-btn-80">
                <button
                  disabled={getSessionStorage('hits_home')}
                  className="round-button yellow hover_black c-black bold mt-lg-5 mt-3 mb-4 with-arrow-white d-inline-flex align-items-center justify-content-center"
                  onClick={() => {
                    setSessionStorage('start_date', startDate.getTime());
                    setSessionStorage('end_date', endDate.getTime());

                    navigate(
                      `/EquipperSpotlight/${selectedEquipper.user_name}`
                    );
                  }}
                >
                  {t('Explore_equipper_catalog')}
                </button>
              </div>
            </div>
          </div>
        </div>
      </RenderIf>
      <div className="container">
        <div className="container-card horizontal-scroll-wrapper squares mt-5 rental_centers">
          <div className="title_home bordred_title">
            <h2 className="t-header-h2 c-fake-black mb-1">
              {t('Contractors_who_trust_us')}
            </h2>
          </div>

          <Swipe
            showArrow={contractors.length > 6 || isMobile}
            id="scrollmenu-contractors"
            className="scrollmenu scrollmenu-contractors"
          >
            {contractors.map((contractor) => (
              <a
                href={contractor.link}
                target="_blank"
                rel="noreferrer"
                className={isMobile ? 'zoom col-4 mr-2' : 'zoom col-2 mr-2'}
              >
                <CustomImage
                  imageUrl={contractor.src}
                  alt="contractor"
                  className="w-100"
                />
              </a>
            ))}
          </Swipe>
        </div>
      </div>
      <div className="container-categories">
        <div className="container">
          <div className="container-card squares mt-lg-5 rental_centers">
            <div className="title_home bordred_title">
              <h2 className="t-header-h2 c-fake-black mb-1">
                {t('Partnering_with')}
              </h2>
            </div>
            <PartnerCarousel
              carouselItems={carouselItems}
              isMobile={isMobile}
              isDesktop={isDesktop}
            />
          </div>
        </div>
      </div>
    </>
  );
}
