import React, { useEffect, useState } from 'react';
import RecommendationsSection from '../company_spotlight/Recommendations_section.jsx';
import { useParams } from 'react-router-dom';
import NeedInformation from '../home/<USER>';
import ReclamationProvider from '../../shared/context/Reclamation_context.jsx';
import ToloIsLoading from '../../shared/components/cards/Tolo_is_loading.jsx';

import { index } from '../../shared/helpers/Algolia_helper.js';
import { useSearchContext } from '../../shared/context/Search_context.jsx';
import RenderIf from '../../shared/components/Render_if.jsx';
import SignInModal from '../../shared/components/modals/Sign_in_modal.jsx';
import { getCookies, setCookies } from '../../shared/helpers/Cookies.js';
import ProjectProvider from '../../shared/context/Project_context.jsx';
import CreditCheckFormProvider from '../../shared/context/Credit_check_form_context.jsx';
import BookingModal from '../../shared/components/modals/Booking_modal.jsx';
import { useLodger } from '../../shared/context/Lodger_context.jsx';
import ScrollToTop from '../../shared/components/Scroll_to_top.jsx';
import BreadCrumb from '../../shared/components/Bread_crumb.jsx';
import { Box, Container } from '@mui/material';
import EquipmentDetailsPageSection from '../../shared/components/equipment/Equipment_details_page_section.jsx';
import EquipmentDetailsPageImageSection from '../../shared/components/equipment/Equipment_details_page_image_section.jsx';
import DescriptionPanelSection from '../../shared/components/equipment/Description_pannel_section.jsx';
import { changeLanguage } from '../../shared/i18n.js';
import NotFoundPage from '../../features/not_found/Not_found_page.jsx';
import NoFound from '../../style/assets/img/no_found.png';

export default function EquipmentDetails({
  t,
  detectedLanguage,
  setShowFPModal,
  showFPModal,
  signIn,
  item,
  role
}) {
  const params = useParams();
  const { getEquipperById } = useSearchContext();

  // Check if user has required cookies for equipment details access
  const checkAccessPermission = () => {
    const currentLang = getCookies('lang');
    const currentCountry = getCookies('country');

    return currentLang === 'en' && currentCountry === 'US';
  };

  // If user doesn't have the required cookies, show access restriction message
  if (!checkAccessPermission()) {
    return (
      <ScrollToTop>
        <NotFoundPage
          t={t}
          text="Feature_not_accessible_in_region"
          description="Access_not_available"
          img={NoFound}
        />
      </ScrollToTop>
    );
  }

  // Force English language and US country for equipment details pages only
  useEffect(() => {
    const defaultLang = 'en';
    const defaultCountry = 'US';

    // Only force language if not already set to English
    const currentLang = getCookies('lang');
    const currentCountry = getCookies('country');

    if (currentLang !== defaultLang || currentCountry !== defaultCountry) {
      setCookies('lang', defaultLang, 31536000); // 1 year in seconds
      setCookies('country', defaultCountry, 31536000);
      changeLanguage(defaultLang);
    }
  }, []); // Run only once when component mounts

  // Extract equipment ID and equipper ID from params.id (format: equipmentId_equipperId)
  const equipmentId = item
    ? item.objectID
    : params.id?.includes('_')
    ? params.id.split('_')[0]
    : params.id;
  const equipperId = item
    ? item.equipper_id
    : params.id?.includes('_')
    ? params.id.split('_')[1]
    : null;
  const { GetLodgerPersonalInfo } = useLodger();
  const [equipper, setEquipper] = useState(null);
  const [showSignIn, setShowSignIn] = useState(false);
  const [equipment, setEquipment] = useState(null);

  const [lodger, setLodger] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [show, setShow] = useState();
  const token = getCookies('token');

  const handleShowBooking = () => {
    setShow(!show);
  };

  useEffect(() => {
    async function fetchData() {
      const { status, data } = await GetLodgerPersonalInfo();
      if (status === 200) {
        setLodger(data);
      }
    }

    if (token) {
      fetchData();
    }
  }, [token]);

  useEffect(() => {
    async function fetchEquipmentData() {
      setIsLoading(true);

      try {
        // First, try to get equipper data (but don't let this block equipment fetching)
        if (equipperId) {
          try {
            const { status, data } = await getEquipperById(equipperId);
            if (status === 200 && data) {
              setEquipper(data);
            }
          } catch (equipperError) {
            console.warn('⚠️ Failed to fetch equipper data:', equipperError);
          }
        }

        // Always try to get equipment data, regardless of equipper status
        // If item prop is provided, use it directly
        if (item) {
          setEquipment(item);
        } else {
          if (!equipmentId || equipmentId.trim() === '') {
            setEquipment(null);
            return;
          }

          const { hits } = await index.search(equipmentId);
          if (hits && hits.length > 0) {
            setEquipment(hits[0]);
          } else {
            // Try searching with objectID field specifically
            const alternativeSearch = await index.search('', {
              filters: `objectID:${equipmentId}`
            });
            if (alternativeSearch.hits && alternativeSearch.hits.length > 0) {
              setEquipment(alternativeSearch.hits[0]);
            } else {
              setEquipment(null);
            }
          }
        }
      } catch (error) {
        setEquipment(null);
      } finally {
        setIsLoading(false);
      }
    }

    fetchEquipmentData();
  }, [equipmentId, equipperId, item]);

  if (isLoading) {
    return <ToloIsLoading />;
  }

  // Don't render if essential data is missing
  if (!equipment) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <p>{t ? t('Equipment_not_found') : 'Equipment not found'}</p>
      </div>
    );
  }
  return (
    <ScrollToTop>
      <BreadCrumb
        t={t}
        items={[
          {
            label: `${
              detectedLanguage === 'fr' ? equipment?.name_fr : equipment?.name
            }  `
          }
        ]}
      />
      <Box
        sx={{
          bgcolor: 'background.paper',
          display: 'flex',
          justifyContent: 'center',
          width: '100%',
          flexDirection: { xs: 'column', md: 'row' },
          mt: 2
        }}
      >
        <Container disableGutters maxWidth="lg" sx={{ position: 'relative' }}>
          {/* Main Content - Two Column Layout */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', md: 'row' },
              mt: 2
            }}
          >
            <Box sx={{ flex: 2, mr: { md: 2 }, mb: { xs: 2, md: 0 } }}>
              <EquipmentDetailsPageSection
                equipment={equipment}
                detectedLanguage={detectedLanguage}
                equipper={equipper}
                t={t}
              />
            </Box>
            <Box sx={{ flex: 1 }}>
              <EquipmentDetailsPageImageSection
                token={token}
                handleShowBooking={handleShowBooking}
                setShowSignIn={setShowSignIn}
                detectedLanguage={detectedLanguage}
                equipment={equipment}
                t={t}
                role={role}
              />
            </Box>
          </Box>

          {/* Description Sections */}
          <Box sx={{ mt: 2 }}>
            <DescriptionPanelSection
              equipment={equipment}
              detectedLanguage={detectedLanguage}
              t={t}
            />
          </Box>
        </Container>
      </Box>
      <RenderIf condition={equipment ? equipment.objectID : params.id}>
        <div className="row">
          <div className="col-lg-10 mx-auto">
            <div className="company-spotlight--similarProduct">
              <h2 className="t-header-h3 bold c-fake-black title-search m-4">
                {`${t('Related_products')}`}
              </h2>
              <RecommendationsSection
                objectID={equipment ? equipment.objectID : params.id}
              />
            </div>
          </div>
        </div>
      </RenderIf>
      <ReclamationProvider>
        <div
          className="need-for-information-equipements"
          style={{ marginTop: '32px' }}
        >
          <Container disableGutters maxWidth="lg">
            <NeedInformation t={t} />
          </Container>
        </div>
      </ReclamationProvider>

      <RenderIf condition={show && token}>
        <ProjectProvider>
          <CreditCheckFormProvider>
            <BookingModal
              handleClose={handleShowBooking}
              data={{
                ...equipment,
                ...lodger
              }}
              isOpen={show}
              t={t}
              hoursPicker={equipper?.work_hours}
              detectLanguage={detectedLanguage}
            />
          </CreditCheckFormProvider>
        </ProjectProvider>
      </RenderIf>
      <SignInModal
        setShowFPModal={setShowFPModal}
        showFPModal={showFPModal}
        setShow={setShowSignIn}
        show={showSignIn}
        signIn={signIn}
        t={t}
      />
    </ScrollToTop>
  );
}
